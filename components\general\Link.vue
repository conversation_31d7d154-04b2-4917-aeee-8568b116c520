<script setup lang="ts">
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['on-click'])
</script>

<template>
  <a
    :id="props.id"
    class="text-xs inline-flex items-center font-semibold cursor-pointer text-info-500 space-x-2 hover:text-info-400"
    @click="emit('on-click')"
  >
    <slot name="prefix"/>
    <p>{{ props.label }}</p>
  </a>
</template>

<style scoped></style>