<template>
	<div class="space-y-4">
		<p class="text-sm text-gray-600 mb-4">Connect your MDVR account to integrate with the platform.</p>
		<form @submit.prevent="handleLogin" class="space-y-4">
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
				<general-text-input v-model="formData.username" placeholder="Enter username" :error="error" />
			</div>
			<div>
				<label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
				<general-text-input v-model="formData.password" type="password" placeholder="Enter password" :error="error" />
			</div>
			<div class="flex items-center gap-2">
				<general-checkbox v-model="formData.remember" color="blue" />
				<label class="text-sm text-gray-600">Remember me</label>
			</div>
			<div v-if="error" class="text-sm text-red-600">{{ error }}</div>
			<div class="pt-2">
				<Button type="submit" variant="primary" :loading="loading" label="Connect to MDVR" class="w-full" />
			</div>
		</form>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Button from '~/components/general/Button.vue'

const emit = defineEmits(['success'])
const loading = ref(false)
const error = ref('')
const formData = ref({
	username: '',
	password: '',
	remember: false
})

const handleLogin = async () => {
	try {
		error.value = ''
		loading.value = true

		const response = await $fetch('/api/mdvr/login', {
			method: 'POST',
			body: {
				username: formData.value.username,
				password: formData.value.password,
				remember: formData.value.remember
			}
		})

		if (response.status === 'success') {
			formData.value.username = ''
			formData.value.password = ''
			formData.value.remember = false
			emit('success')
		}
	} catch (err: any) {
		error.value = err.data?.message || 'Connection failed. Please try again.'
	} finally {
		loading.value = false
	}
}
</script>
