<script setup lang="ts">
import VueDatePicker, { type DatePickerInstance } from '@vuepic/vue-datepicker'
import { format, getHours, isSameDay } from 'date-fns';

const props = defineProps({
  modelValue: {
    type: [Object as () => null, Date],
    default: null
  },
  id: {
    type: String,
    required: true
  },
  required: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  minDate: {
    type: Date,
    default: undefined
  },
  maxDate: {
    type: Date,
    default: undefined
  },
  timePicker: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:model-value'])

const initialDateTime = ref()
const dateTime = ref()
const selectedDate = ref()
const datePicker = ref<DatePickerInstance>()

const minTime = (hour: number) => {
  const isSameAsMin =
    props.minDate &&
    isSameDay(selectedDate.value, props.minDate) &&
    hour === getHours(props.minDate)

  return isSameAsMin ? {
    hours: props.minDate?.getHours(),
    minutes: props.minDate?.getMinutes()
  } : { hours: 0, minutes: 0 }
}

const maxTime = (hour: number) => {
  const isSameAsMax =
    props.maxDate &&
    isSameDay(selectedDate.value, props.maxDate) &&
    hour === getHours(props.maxDate)

  return isSameAsMax ? {
    hours: props.maxDate?.getHours(),
    minutes: props.maxDate?.getMinutes()
  } : { hours: 23, minutes: 59 }
}

const placeholderFormat = (date: Date) => {
  if (!date) return ''
  const formatDateTime = `dd/MM/yyyy${props.timePicker ? ', HH:mm' : ''}`
  return format(date, formatDateTime)
}

function onOpen() {
  initialDateTime.value = dateTime.value
  if (!dateTime.value) {
    selectedDate.value = undefined
    dateTime.value = props.minDate ?? new Date()
  }
}

function onClosed() {
  dateTime.value = initialDateTime.value
}

watch(() => props.modelValue, (value) => {
  dateTime.value = value
}, { immediate: true })
</script>

<template>
  <div class="flex flex-col">
    <label v-if="props.label" :for="id" class="mb-1.5 text-sm font-medium text-gray-700"
      @click="datePicker?.openMenu()">
      {{ props.label }}
      <span v-if="props.required" class="text-primary-500">*</span>
    </label>

    <vue-date-picker ref="datePicker" v-model="dateTime" :id="props.id" :enable-time-picker="props.timePicker"
      time-picker-inline :format="placeholderFormat" :min-date="props.minDate" :max-date="props.maxDate"
      :placeholder="props.placeholder" :disabled="props.disabled"
      input-class-name="!py-2 !rounded-lg focus:!ring-primary-500" class="custom-date-picker"
      @date-update="selectedDate = $event" @open="onOpen" @closed="onClosed"
      @cleared="emit('update:model-value', undefined)">
      <template #time-picker="{ time, updateTime }">
        <div class="px-3 space-y-3">
          <hr>

          <div class="flex justify-between items-center space-x-3">
            <p>{{ format(selectedDate ?? new Date(), 'MMM dd, yyyy') }}</p>
            <general-time-input :model-value="time" :min-time="minTime(time.hours)" :max-time="maxTime(time.hours)"
              @update:model-value="($event) => {
                updateTime($event.hours)
                updateTime($event.minutes, false)
              }" />
          </div>

          <hr>
        </div>
      </template>

      <template #action-row="{ internalModelValue, selectDate }">
        <div class="grid grid-cols-2 gap-3 w-full">
          <general-outlined-button label="Cancel" @on-click="datePicker?.closeMenu()" />
          <general-button label="Confirm" :disabled="!internalModelValue" @on-click="() => {
            selectDate()
            emit('update:model-value', internalModelValue)
          }" />
        </div>
      </template>
    </vue-date-picker>
  </div>
</template>

<style scoped>
.custom-date-picker :deep(input) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__month_year_row) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__calendar_header) {
  font-weight: 500 !important;
}

.custom-date-picker :deep(.dp__calendar) {
  font-weight: 400 !important;
}

.custom-date-picker :deep(.dp__theme_light) {
  --dp-text-color: #344054;
  --dp-primary-color: var(--primary-600);
  --dp-range-between-dates-background-color: var(--primary-50);
  --dp-range-between-dates-text-color: var(--primary-700);
}
</style>