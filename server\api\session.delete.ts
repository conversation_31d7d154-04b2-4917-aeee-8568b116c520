import { defineE<PERSON><PERSON><PERSON><PERSON>, deleteCookie, getCookie } from 'h3';
import { logoutFromTraccar } from '../utils/traccar';
// import { db } from '../database/drizzle';
// import { and, eq } from 'drizzle-orm';

export default defineEventHandler(async (event) => {
  // Handle Traccar logout
  const sessionId = getCookie(event, 'traccar.token');
  if (sessionId) {
    await logoutFromTraccar(sessionId);
    deleteCookie(event, 'traccar.token');

    // Update integration status
    // const userId = getCookie(event, 'user_id');
  }

  // Handle Telematics logout
  const telematicsUserId = getCookie(event, 'user_id');
  if (telematicsUserId) {
    deleteCookie(event, 'telematics.token');
  }

  deleteCookie(event, 'user_id');

  // Handle MDVR Logout
  const mdvrToken = getCookie(event, 'mdvr.token')

  if (mdvrToken) deleteCookie(event, 'mdvr.token')

  return { status: 'ok' };
});