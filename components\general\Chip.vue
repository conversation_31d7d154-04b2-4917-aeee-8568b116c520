<script setup lang="ts">
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: 'primary'
  }
})
</script>

<template>
  <div
    class="py-0.5 px-2 space-x-1 w-fit flex items-center text-xs rounded-full cursor-default select-none transition-all"
    :class="`bg-${color}-50 text-${color}-700`"
  >
    <slot name="prefix"/>
    <p>{{ label }}</p>
    <slot name="suffix"/>
  </div>

</template>

<style scoped>

</style>