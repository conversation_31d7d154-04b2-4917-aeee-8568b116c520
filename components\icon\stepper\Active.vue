<script setup lang="ts">
const props = defineProps({
  size: {
    type: Number,
    default: 40
  }
})
</script> 

<template>
  <div>
    <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 40 40" fill="none">
      <path d="M1.625 20C1.625 9.85177 9.85177 1.625 20 1.625C30.1482 1.625 38.375 9.85177 38.375 20C38.375 30.1482 30.1482 38.375 20 38.375C9.85177 38.375 1.625 30.1482 1.625 20Z" fill="var(--primary-500)"/>
      <path d="M1.625 20C1.625 9.85177 9.85177 1.625 20 1.625C30.1482 1.625 38.375 9.85177 38.375 20C38.375 30.1482 30.1482 38.375 20 38.375C9.85177 38.375 1.625 30.1482 1.625 20Z" stroke="var(--primary-100)" stroke-width="3.25"/>
      <circle cx="20" cy="20" r="6" fill="white"/>
    </svg>
  </div>
</template>

<style scoped></style>