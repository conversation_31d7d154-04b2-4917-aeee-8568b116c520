import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getC<PERSON>ie } from 'h3';
import { getDevicesFromTraccar } from '../utils/traccar';
import { getDevicesFromTelematics, getGroupsFromTelematics } from '../utils/telematics';
import { getDevicesFromMDVR } from '../utils/mdvr';
import { db } from '../database/drizzle';
import { telematicsUserData } from '../database/schema';
import { eq } from 'drizzle-orm';
import type { TraccarDevice, TelematicsDevice, UnifiedDevice, MDVRDevice } from '~/types/device';
import type { TelematicsGroup } from '~/types/group';

function convertTelematicsToUnified(device: TelematicsDevice & { groupId?: number }): UnifiedDevice {
  const getStatus = (device: TelematicsDevice): string => {
    const onlineStatus = device.online.toLowerCase();
    const speed = device.speed;
    const timestamp = device.timestamp * 1000; // Convert to milliseconds
    const now = Date.now();
    const timeDiff = now - timestamp;

    // If device hasn't reported in 5 minutes, consider it offline
    if (timeDiff > 300000) {
      return 'offline';
    }

    // If device is moving
    if (speed > 0) {
      return 'online';
    }

    // If device is stopped but recently reported
    if (onlineStatus === 'online' || onlineStatus === 'ack') {
      return 'waiting';
    }

    return 'offline';
  };

  return {
    id: device.id,
    name: device.name,
    groupId: device.groupId,
    status: getStatus(device),
    telematics: device,
    traccar: undefined,
    mdvr: undefined,
    lat: device.lat,
    lng: device.lng,
    speed: device.speed,
    altitude: device.altitude,
    course: device.course,
    source: 'telematics'
  };
}


function convertTraccarToUnified(device: TraccarDevice): UnifiedDevice {
  return {
    id: device.id,
    name: device.name,
    groupId: device.groupId,
    status: device.status,
    telematics: undefined,
    mdvr: undefined,
    traccar: device,
    lat: 0,
    lng: 0,
    speed: 0,
    altitude: 0,
    course: 0,
    lastUpdate: device.lastUpdate || undefined,
    source: 'traccar'
  };
}

function convertMDVRToUnified(device: MDVRDevice): UnifiedDevice {
  const getStatus = (device: MDVRDevice): string => {
    const now = new Date();
    const lastOnline = new Date(device.dtu); // Use dtu (device time update) instead of lastonlinetime
    const timeDiff = now.getTime() - lastOnline.getTime();
    const speed = parseFloat(device.speed);

    try {
      // Parse lastStatusJson to get more detailed status
      const lastStatus = JSON.parse(device.lastStatusJson);

      // Check if device is actively reporting (within last 5 minutes)
      if (timeDiff <= 300000) {
        // Check if device is moving
        if (speed > 0) {
          return 'online';
        }

        // Check if mobile module is connected (mobile: "1" means connected)
        if (lastStatus.module?.mobile === "1") {
          return 'waiting';
        }
      }

      // If device hasn't reported recently or mobile module is not connected
      return 'offline';
    } catch (error) {
      // Fallback if JSON parsing fails
      if (timeDiff <= 300000) {
        return speed > 0 ? 'online' : 'waiting';
      }
      return 'offline';
    }
  };

  return {
    id: parseInt(device.deviceno),
    name: device.devicename,
    groupId: undefined,
    status: getStatus(device),
    telematics: undefined,
    traccar: undefined,
    mdvr: device,
    lat: parseFloat(device.latitude),
    lng: parseFloat(device.longitude),
    speed: parseFloat(device.speed),
    altitude: parseFloat(device.altitude),
    course: parseFloat(device.direct),
    lastUpdate: device.dtu,
    source: 'mdvr'
  };
}

export default defineEventHandler(async (event) => {
  const traccarSessionId = getCookie(event, 'traccar.token');
  const telematicsSessionId = getCookie(event, 'telematics.token');
  const mdvrSessionId = getCookie(event, 'mdvr.token');

  const devices: UnifiedDevice[] = [];

  // Fetch Traccar devices if session exists
  if (traccarSessionId) {
    try {
      const traccarDevices = await getDevicesFromTraccar(traccarSessionId);
      devices.push(...traccarDevices.map(convertTraccarToUnified));
    } catch (error) {
      console.error('Failed to fetch Traccar devices:', error);
    }
  }

  if (telematicsSessionId) {
    try {
      // Get devices and assign them to groups
      const telematicsDevices = await getDevicesFromTelematics(telematicsSessionId);
      telematicsDevices.forEach(deviceGroup => {
        const groupId = deviceGroup.id;
        const groupDevices = deviceGroup.items.map(device => ({
          ...device,
          groupId: groupId // Assign the group ID to each device
        }));
        devices.push(...groupDevices.map(convertTelematicsToUnified));
      });
    } catch (error) {
      console.error('Failed to fetch Telematics devices:', error);
    }
  }

  if (mdvrSessionId) {
    try {
      const mdvrDevices = await getDevicesFromMDVR(mdvrSessionId);
      devices.push(...mdvrDevices.map(convertMDVRToUnified));
    } catch (error) {
      console.error('Failed to fetch MDVR devices:', error);
    }
  }

  return devices;
});
