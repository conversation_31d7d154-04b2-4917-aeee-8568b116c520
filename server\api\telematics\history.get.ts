import { defineEvent<PERSON><PERSON><PERSON>, getCookie } from 'h3';
import { getDeviceHistory } from "~/server/utils/telematics";

export default defineEventHandler(async (event) => {
	const telematicsSessionId = getCookie(event, 'telematics.token');
	const query = getQuery(event);
	const { 
		device_id, 
		from_date, 
		from_time, 
		to_date, 
		to_time,
		snap_to_road = 'false'
	} = query;

	if (!telematicsSessionId) {
		throw createError({
			statusCode: 401,
			message: 'Unauthorized - Missing telematics session'
		});
	}

	if (!device_id || !from_date || !from_time || !to_date || !to_time) {
		throw createError({
			statusCode: 400,
			message: 'Missing required parameters'
		});
	}

	try {
		const history = await getDeviceHistory(
			telematicsSessionId,
			Number(device_id),
			from_date as string,
			from_time as string,
			to_date as string,
			to_time as string,
			snap_to_road === 'true'
		);

		return history;
	} catch (error: any) {
		console.error('Failed to fetch device history:', error);
		throw createError({
			statusCode: 500,
			message: error.message || 'Failed to fetch device history'
		});
	}
});