<template>
	<NuxtLink 
		:to="destination"
		:id="id"
		class="group flex items-center w-full rounded-lg transition-all duration-200"
		:class="[
			isActive 
				? 'bg-blue-50 text-blue-600'
				: 'text-gray-500 hover:bg-gray-50 hover:text-gray-900'
		]"
	>
		<div class="flex items-center gap-3 p-2.5 w-full">
			<div class="flex-none transition-transform group-hover:scale-105">
				<slot name="icon" />
			</div>
			<span v-if="isShowName" 
				class="flex-1 text-sm font-medium truncate transition-colors"
				:class="isActive ? 'text-blue-600' : 'text-gray-500 group-hover:text-gray-900'"
			>
				{{ name }}
			</span>
		</div>
	</NuxtLink>
</template>

<script setup lang="ts">
const route = useRoute()

const props = defineProps<{
	id: string
	name: string
	destination: string
	isShowName: boolean
}>()

const isActive = computed(() => {
	return route.path.startsWith(props.destination)
})
</script>