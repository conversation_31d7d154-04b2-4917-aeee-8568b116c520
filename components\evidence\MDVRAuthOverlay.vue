<template>
	<general-modal 
		id="mdvr-auth-overlay" 
		title="MDVR Authentication Required" 
		:is-has-close="true" 
		class-modal="max-w-md"
		@mounted="onModalMounted">
		<template #body>
			<div class="space-y-4">
				<div class="text-center mb-4">
					<div class="w-12 h-12 mx-auto mb-3 bg-yellow-100 rounded-full flex items-center justify-center">
						<icon-alert class="w-6 h-6 text-yellow-600" />
					</div>
					<p class="text-sm text-gray-600">
						Your MDVR session has expired or is invalid. Please authenticate to continue accessing evidence data.
					</p>
				</div>
				<MDVRLogin @success="handleAuthSuccess" />
			</div>
		</template>
	</general-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import MDVRLogin from '~/components/integrations/MDVRLogin.vue'
import IconAlert from '~/components/icon/Alert.vue'
import type { ElementEvent } from '~/types/element'

const emit = defineEmits(['auth-success', 'mounted'])

const modal = ref<ElementEvent | null>(null)

const onModalMounted = (event: ElementEvent) => {
	modal.value = event
	emit('mounted', event)
}

const handleAuthSuccess = () => {
	// Close the modal
	modal.value?.hide()
	
	// Emit success event to parent component
	emit('auth-success')
}

// Expose methods for parent component to control the modal
defineExpose({
	show: () => modal.value?.show(),
	hide: () => modal.value?.hide(),
	toggle: () => modal.value?.toggle()
})
</script>
