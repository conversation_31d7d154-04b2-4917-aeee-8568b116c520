import { add } from "date-fns"
import { and, eq } from "drizzle-orm"
import { db } from "~/server/database/drizzle"
import { integrationTokens } from "~/server/database/schema"

export default defineEventHandler(async (event) => {
	const userId = getCookie(event, 'user_id')

	if (!userId) {
		throw createError({
			statusCode: 401,
			statusMessage: 'Unauthorized'
		})
	}

	const body = await readBody(event)

	try {
		const response = await fetch('https://telematics.transtrack.id/api/login', {
			method: 'POST',
			body: new URLSearchParams({ ...body })
		})

		const data = await response.json()

		if (!data.status || data.status !== 1) {
			throw new Error('Invalid telematics credentials');
		}

		// Re-init telematics token
		const expiry = add(new Date(), { days: 7 })

		await db.delete(integrationTokens).where(and(
			eq(integrationTokens.userId, userId),
			eq(integrationTokens.provider, 'telematics')
		))

		await db.insert(integrationTokens).values({
			userId: userId,
			provider: 'telematics',
			token: data.user_api_hash,
			pid: data.status.toString(),
			guid: data.status.toString(),
			username: body.email,
			expiry
		})

		// Set telematics cookie
		setCookie(event, 'telematics.token', data.user_api_hash, { httpOnly: true, path: '/' })

		return {
			status: 'success',
			message: 'Telematics integration connected successfully'
		}
	} catch (error: any) {
		throw createError({
			statusCode: error.status || 500,
			message: error.statusText || 'Connection failed. Please try again.'
		})
	}
})
