import { defineEvent<PERSON><PERSON><PERSON>, createError } from 'h3'
import { db } from '../../database/drizzle'
import { integrationTokens } from '../../database/schema'
import { eq, and, gt } from 'drizzle-orm'
import { requireUserSession } from '../../utils/session'

export default defineEventHandler(async (event) => {
	const userId = getCookie(event, 'user_id')

	if (!userId) {
		throw createError({
			statusCode: 401,
			statusMessage: 'Unauthorized'
		})
	}

	const token = await db.query.integrationTokens.findFirst({
		where: and(
			eq(integrationTokens.userId, userId),
			eq(integrationTokens.provider, 'telematics'),
			gt(integrationTokens.expiry, new Date())
		)
	})

	return {
		status: 'success',
		data: {
			isConnected: !!token,
			username: token?.username,
			expiry: token?.expiry
		}
	}
})