import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, get<PERSON><PERSON><PERSON>, createError } from 'h3';
import { getPositionsFromTraccar } from '../utils/traccar';
import { getLatestPositionsFromTelematics } from '../utils/telematics';
import { db } from '../database/drizzle';
import { telematicsUserData } from '../database/schema';
import { eq } from 'drizzle-orm';
import type { Position } from '~/types/position';

function convertTelematicsToPosition(item: any): Position {
	// Parse tail data if it exists
	let tail = ''
	if (item.latest_positions) {
		// Use latest_positions if available as it's already in the correct format
		tail = item.latest_positions
	} else if (item.tail) {
		try {
			// Parse tail JSON and convert to the required format
			const tailData = JSON.parse(item.tail)
			tail = tailData.map((coord: { lat: string, lng: string }) => `${coord.lat}/${coord.lng}`).join(';')
		} catch (error) {
			console.error('Failed to parse tail data:', error)
		}
	}

	return {
		id: item.id,
		deviceId: item.id,
		protocol: 'telematics',
		deviceTime: item.time,
		fixTime: item.time,
		serverTime: item.time,
		outdated: false,
		valid: true,
		latitude: item.lat,
		longitude: item.lng,
		altitude: item.altitude,
		speed: item.speed,
		course: item.course,
		address: null,
		accuracy: 0,
		network: null,
		attributes: {},
		tail: tail
	};
}

export default defineEventHandler(async (event) => {
	const positions: Position[] = [];
	const traccarSessionId = getCookie(event, 'traccar.token');
	const telematicsSessionId = getCookie(event, 'telematics.token');

	// Fetch Traccar positions if session exists
	if (traccarSessionId) {
		try {
			const traccarPositions = await getPositionsFromTraccar(traccarSessionId);
			positions.push(...traccarPositions);
		} catch (error) {
			console.error('Failed to fetch Traccar positions:', error);
		}
	}

	// Fetch Telematics positions if user exists
	if (telematicsSessionId) {
		try {
			const telematicsData = await getLatestPositionsFromTelematics(telematicsSessionId);
			const telematicsPositions = telematicsData.items.map(convertTelematicsToPosition);
			positions.push(...telematicsPositions);
		} catch (error) {
			console.error('Failed to fetch Telematics positions:', error);
		}
	}

	return positions;
});