<script setup lang="ts">
import {useUtilsStore} from "~/store/utils";

const props = defineProps({
  src: {
    type: [String, Object as () => null],
    default: null
  },
  downloadUrl : {
    type: [Object as () => null, String],
    default: null
  },
  width: {
    type: [Number, String],
    default: 120
  },
  height: {
    type: [Number, String],
    default: 120
  },
  isAutoSize: {
    type: Boolean,
    default: false
  },
  isDownloadAble: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['on-click'])

const $utils = useUtilsStore()

async function onClickDownload() {
  await $utils.downloadImage(props.downloadUrl ?? props.src!, 'E-POD')
}
</script>

<template>
  <div
    class="rounded overflow-hidden relative border"
    :style="isAutoSize ? '' : `min-width: ${props.width}px; height: ${props.height}px`"
    @click="emit('on-click')"
  >
    <button
      v-if="props.isDownloadAble"
      type="button"
      :disabled="$utils.isLoading.download"
      class="h-8 w-8 bg-white border border-gray-300 rounded-md flex absolute right-2 bottom-2 transition hover:bg-gray-200"
      @click.stop="onClickDownload"
    >
      <svg
        v-if="$utils.isLoading.download"
        aria-hidden="true"
        class="w-4 h-4 animate-spin text-gray-300 fill-white m-auto"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
        <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
      </svg>
      <icon-download-cloud v-else size="16" class="m-auto stroke-gray-900"/>
    </button>

    <div
      v-if="props.src"
      class="bg-center bg-cover bg-no-repeat w-full h-full"
      :style="`background-image: url(${props.src})`"
    />

    <div
      v-else
      class="w-full h-full"
    >
      <slot name="placeholder"/>

      <div
        v-if="!$slots.placeholder"
        class="w-full h-full flex items-center justify-center relative"
      >
        <div class="absolute top-0 bottom-0 left-0 right-0 bg-center bg-cover opacity-50"/>

        <div class="flex flex-col items-center space-y-1 relative">
          <icon-camera-off class="stroke-gray-900"/>
          <p class="text-xs select-none">No Image</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

</style>