<template>
  <div class="bg-white rounded-lg border border-gray-100 overflow-visible cursor-pointer"
    @click="$emit('toggleFollow')">
    <div class="flex items-center gap-2 p-3">
      <Checkbox :model-value="visible" @update:model-value="$emit('toggleVisibility')" @click.stop color="blue"
        class="flex-none" />

      <div class="flex items-center gap-2 flex-1 min-w-0">
        <div class="w-1 self-stretch rounded-full" :class="statusColorClass" />
        <div class="flex-1 min-w-0">
          <div class="font-medium text-gray-900">{{ device.name }}</div>
          <div class="text-sm text-gray-500">{{
            formatDate(getLastUpdateTime)
          }}
          </div>
        </div>
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-1">
            <div class="text-sm font-medium text-gray-900">{{
              position?.speed ? formatSpeed(position.speed) : '0 Knot'
            }}
            </div>
            <button type="button" :id="`collapse-${device.id}`" :data-collapse-toggle="`collapse-target-${device.id}`"
              class="p-1 hover:bg-gray-100 rounded-lg mx-1" @click.stop>
              <icon-chevron-down :class="[
                'w-6 h-6 transition-transform stroke-gray-400',
                isExpanded ? 'rotate-180' : 'rotate-0'
              ]" />
            </button>
          </div>

          <Dropdown :id="`device-${device.id}-dropdown`">
            <template #activator="{ toggle }">
              <button type="button" :id="`collapse-${device.id}`" :data-collapse-toggle="`collapse-target-${device.id}`"
                class="p-1 hover:bg-gray-100 rounded-lg" @click.stop="toggle">
                <icon-dots-vertical class="w-5 h-5 text-gray-400" />

              </button>
            </template>
            <template #content="{ close }">
              <div class="p-2 min-w-[200px] flex flex-col gap-2">
                <button
                  class="w-full px-4 py-2 text-sm font-medium text-white bg-primary-500 rounded-lg hover:bg-primary-600"
                  @click.stop="$emit('addToGroup'); close()">
                  Add to Group
                </button>
                <button class="w-full px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-50 rounded-lg"
                  @click.stop="navigateToReport(); close()">
                  View Report
                </button>
                <button class="w-full px-4 py-2 text-sm font-medium text-gray-900 hover:bg-gray-50 rounded-lg"
                  @click.stop="navigateToDashboard(); close()">
                  Dashboard
                </button>
              </div>
            </template>
          </Dropdown>
        </div>
      </div>
    </div>

    <div :id="`collapse-target-${device.id}`" class="hidden">
      <div class="px-4 pb-4 border-t border-gray-100">
        <div class="grid grid-cols-3 gap-y-4 gap-x-6 mt-4">

          <div>
            <div class="text-sm text-gray-500 mb-1">Vessel Status</div>
            <div class="text-sm font-medium text-gray-900">
              <span class="inline-flex items-center gap-1.5">
                <span class="w-2 h-2 rounded-full" :class="{
                  'bg-green-500': device.status === 'online',
                  'bg-red-500': device.status === 'offline',
                  'bg-gray-500': device.status === 'ack',
                  'bg-yellow-500': device.status === 'idle'
                }" />
                {{ device.status }}
              </span>
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Engine Status</div>
            <div class="text-sm font-medium text-gray-900">{{ device.traccar?.attributes?.engineStatus || 'Off' }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Asset Type</div>
            <div class="text-sm font-medium text-gray-900">
              {{ device.traccar?.category || device.telematics?.icon_type || 'Tugboat' }}
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Connection</div>
            <div class="text-sm font-medium text-gray-900">{{ device.traccar?.attributes?.connection || 'GSM' }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Heading</div>
            <div class="text-sm font-medium text-gray-900">{{
              position?.heading ? `${position.heading}°` : 'N/A'
            }}
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Speed</div>
            <div class="text-sm font-medium text-gray-900">
              {{ position?.speed ? formatSpeed(position.speed) : '0.00 Knot' }}
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Longitude</div>
            <div class="text-sm font-medium text-gray-900">
              {{ position?.longitude ? formatCoordinate(position.longitude, false) : 'N/A' }}
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Latitude</div>
            <div class="text-sm font-medium text-gray-900">
              {{ position?.latitude ? formatCoordinate(position.latitude, true) : 'N/A' }}
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500 mb-1">Altitude</div>
            <div class="text-sm font-medium text-gray-900">
              {{ position?.altitude ? formatAltitude(position.altitude) : 'N/A' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Collapse, initFlowbite } from 'flowbite'
import type { CollapseInterface } from 'flowbite'
import Checkbox from '~/components/general/Checkbox.vue'
import IconButton from '~/components/general/IconButton.vue'
import Dropdown from '~/components/general/Dropdown.vue'
import IconChevronDown from '~/components/icon/ChevronDown.vue'
import IconDotsVertical from '~/components/icon/DotsVertical.vue'
import { useRouter } from '#app'

import type { UnifiedDevice } from '~/types/device'

const router = useRouter()
const isExpanded = ref(false)

onMounted(() => {
  initFlowbite()
  const $trigger = document.getElementById(`collapse-${props.device.id}`)
  const $target = document.getElementById(`collapse-target-${props.device.id}`)

  if ($trigger && $target) {
    new Collapse($target, $trigger, {
      onToggle: (instance: CollapseInterface) => {
        isExpanded.value = instance._visible
      }
    })
  }
})

const isDeviceActive = computed(() => {
  return props.device.telematics?.device_data?.active === 1 && props.visible
})

const toggleDeviceActive = () => {
  emit('toggleVisibility')
}

const props = defineProps<{
  device: UnifiedDevice
  selected?: boolean
  visible?: boolean
  following?: boolean
  position?: {
    fixTime: string
    latitude: number
    longitude: number
    altitude: number
    speed: number
    heading: number
  }
}>()

const emit = defineEmits<{
  click: []
  toggleVisibility: []
  toggleFollow: []
  addToGroup: []
}>()

const formatDate = (date: string | undefined | null): string => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleString()
}

const formatSpeed = (speed: number): string => {
  if (!speed) return '0.00 Knot'
  // Convert m/s to km/h (1 m/s = 3.6 km/h)
  return `${(speed).toFixed(2)} Knot`
}

function navigateToReport() {
  router.push(`/dashboard/device/${props.device.id}/report`)
}

function navigateToDashboard() {
  router.push(`/dashboard/device/${props.device.id}`)
}

const getLastUpdateTime = computed(() => {
  if (props.device.mdvr) {
    return props.device.mdvr.lastonlinetime
  }
  return props.device.traccar?.lastUpdate || props.device.telematics?.time
})

const statusColorClass = computed(() => {
  const status = getDeviceStatus()
  return {
    'bg-green-500': status === 'online',
    'bg-red-500': status === 'offline',
    'bg-gray-500': status === 'ack',
    'bg-yellow-500': status === 'idle' || status === 'waiting'
  }
})

const getDeviceStatus = () => {
  // Use the unified status directly since it's already properly converted
  return props.device.status;
}
</script>
