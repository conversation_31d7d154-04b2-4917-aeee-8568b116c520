<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  iconVariant: {
    type: String as () => 'success' | 'info' | 'warning' | 'error',
    default: 'info'
  }
})
</script>

<template>
  <div class="p-3 border rounded-lg flex items-center space-x-3">
    <div
      class="h-12 aspect-square flex items-center justify-center rounded border"
      :class="`bg-${iconVariant}-25 border-${iconVariant}-500 stroke-${iconVariant}-500`"
    >
      <slot name="icon"/>
    </div>

    <div class="space-y-1">
      <p class="text-sm font-semibold text-gray-900">{{ props.title }}</p>
      <p class="text-xs text-gray-500">{{ props.subtitle }}</p>
    </div>
  </div>
</template>

<style scoped></style>