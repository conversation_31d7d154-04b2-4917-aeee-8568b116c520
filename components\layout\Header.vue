<script setup lang="ts">
import {usePageStore} from "~/store/page";
import type {ElementEvent} from "~/types/element";
import {capitalizeString} from "~/utils/functions";

let modalLogout: ElementEvent | null = null

const $page = usePageStore()

const emit = defineEmits(['on-click-open-sidebar'])

const version = computed(() => {
  return useRuntimeConfig().public.clientVersion
})

</script>

<template>
  <div class="w-full flex items-center justify-between">

    <div class="flex items-center">
      <general-icon-button class="block md:hidden mr-4" @click="emit('on-click-open-sidebar')">
        <template #icon>
          <icon-menu/>
        </template>
      </general-icon-button>

      <slot name="header"/>

      <h1 class="text-2xl font-[600]">
        {{ $page.$state.title }}
      </h1>
    </div>

  </div>
</template>

<style scoped></style>
