<template>
	<nav class="flex px-6 py-3 text-gray-600 text-sm" aria-label="Breadcrumb">
		<ol class="inline-flex items-center space-x-1 md:space-x-3">
			<li class="inline-flex items-center">
				<NuxtLink to="/" class="inline-flex items-center hover:text-primary">
					<svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
						<path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
					</svg>
					Dashboard
				</NuxtLink>
			</li>
			<li v-for="(crumb, index) in breadcrumbs" :key="index">
				<div class="flex items-center">
					<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
						<path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
					</svg>
					<NuxtLink 
						v-if="index < breadcrumbs.length - 1" 
						:to="crumb.path"
						class="ml-1 hover:text-primary md:ml-2"
					>
						{{ crumb.title }}
					</NuxtLink>
					<span 
						v-else 
						class="ml-1 text-gray-800 md:ml-2"
					>
						{{ crumb.title }}
					</span>
				</div>
			</li>
		</ol>
	</nav>
</template>

<script setup lang="ts">
interface Breadcrumb {
	name: string;
	path: string;
	title: string;
}

const route = useRoute()

const breadcrumbs = computed<Breadcrumb[]>(() => {
	const pathArray = route.path.split('/')
	const breadcrumbs: Breadcrumb[] = []
	let path = ''

	// Skip empty string and 'dashboard' as we have a fixed home link
	pathArray.forEach((item, index) => {
		if (item !== '' && item !== 'dashboard') {
			path += `/${item}`
			
			// Build the full path including 'dashboard'
			const fullPath = `/dashboard${path}`
			
			breadcrumbs.push({
				name: item,
				path: fullPath,
				title: getBreadcrumbTitle(item, index, pathArray)
			})
		}
	})

	return breadcrumbs
})

function getBreadcrumbTitle(item: string, index: number, pathArray: string[]): string {
	// Special cases for known routes
	if (item === 'device') {
		return 'Device Details'
	}
	
	// Handle dynamic route parameters (e.g., [id])
	if (item.startsWith('[') && item.endsWith(']')) {
		const paramName = item.slice(1, -1)
		const paramValue = route.params[paramName]
		
		// If this is a device ID and it's followed by 'report'
		if (paramName === 'id' && pathArray[index + 1] === 'report') {
			return `Device ${paramValue}`
		}
		
		return String(paramValue || paramName)
	}
	
	// Handle specific pages
	if (item === 'report') {
		return 'History Report'
	}
	
	// Default formatting for other routes
	return item
		.split(/[-_]/)
		.map(word => word.charAt(0).toUpperCase() + word.slice(1))
		.join(' ')
}
</script>