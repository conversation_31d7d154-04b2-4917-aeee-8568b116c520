<script setup lang="ts">

</script>

<template>
  <svg width="138" height="100" viewBox="0 0 138 100" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="69" cy="50" r="50" fill="#EAECF0" />
    <circle cx="16.5" cy="12.5" r="5" fill="#F2F4F7" />
    <circle cx="124" cy="78.75" r="3.75" fill="#F2F4F7" />
    <circle cx="15.875" cy="86.25" r="6.25" fill="#F2F4F7" />
    <circle cx="131.5" cy="28.75" r="6.25" fill="#F2F4F7" />
    <circle cx="119.625" cy="6.875" r="4.375" fill="#F2F4F7" />
    <g filter="url(#filter0_dd_13787_125880)">
      <path fill-rule="evenodd" clip-rule="evenodd"
        d="M71.1786 10C60.7182 10 51.4651 15.1691 45.8343 23.0922C43.9956 22.6586 42.0782 22.4292 40.1071 22.4292C26.3789 22.4292 15.25 33.5581 15.25 47.2863C15.25 61.0146 26.3789 72.1435 40.1071 72.1435C40.1664 72.1435 40.2256 72.1433 40.2848 72.1429H102.25V72.1426C102.25 72.1426 102.25 72.1426 102.25 72.1426C114.262 72.1426 124 62.4048 124 50.3926C124 38.3804 114.262 28.6426 102.25 28.6426C101.397 28.6426 100.555 28.6917 99.7273 28.7873C94.9659 17.7364 83.9757 10 71.1786 10Z"
        fill="#F9FAFB" />
      <circle cx="40.1071" cy="47.2868" r="24.8571" fill="url(#paint0_linear_13787_125880)" />
      <circle cx="71.1788" cy="41.0714" r="31.0714" fill="url(#paint1_linear_13787_125880)" />
      <circle cx="102.25" cy="50.3906" r="21.75" fill="url(#paint2_linear_13787_125880)" />
    </g>
    <g filter="url(#filter1_b_13787_125880)">
      <rect x="51.5" y="52.5" width="35" height="35" rx="17.5" fill="#344054" fill-opacity="0.4" />
      <path
        d="M68.9993 69.9987L65.8837 67.4023C65.4208 67.0166 65.1893 66.8237 65.0229 66.5872C64.8755 66.3778 64.766 66.144 64.6995 65.8966C64.6243 65.6174 64.6243 65.3161 64.6243 64.7136V62.707M68.9993 69.9987L72.115 67.4023C72.5779 67.0166 72.8094 66.8237 72.9758 66.5872C73.1232 66.3778 73.2327 66.144 73.2992 65.8966C73.3744 65.6174 73.3744 65.3161 73.3744 64.7136V62.707M68.9993 69.9987L65.8837 72.5951C65.4208 72.9808 65.1893 73.1737 65.0229 73.4101C64.8755 73.6196 64.766 73.8534 64.6995 74.1008C64.6243 74.38 64.6243 74.6813 64.6243 75.2838V77.2904M68.9993 69.9987L72.115 72.5951C72.5779 72.9808 72.8094 73.1737 72.9758 73.4101C73.1232 73.6196 73.2327 73.8534 73.2992 74.1008C73.3744 74.38 73.3744 74.6813 73.3744 75.2838V77.2904M63.166 62.707H74.8327M63.166 77.2904H74.8327"
        stroke="white" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
    </g>
    <defs>
      <filter id="filter0_dd_13787_125880" x="2.75" y="10" width="133.75" height="87.1445" filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha" />
        <feMorphology radius="2.5" operator="erode" in="SourceAlpha" result="effect1_dropShadow_13787_125880" />
        <feOffset dy="5" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.03 0" />
        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_13787_125880" />
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha" />
        <feMorphology radius="2.5" operator="erode" in="SourceAlpha" result="effect2_dropShadow_13787_125880" />
        <feOffset dy="12.5" />
        <feGaussianBlur stdDeviation="7.5" />
        <feColorMatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0" />
        <feBlend mode="normal" in2="effect1_dropShadow_13787_125880" result="effect2_dropShadow_13787_125880" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_13787_125880" result="shape" />
      </filter>
      <filter id="filter1_b_13787_125880" x="46.5" y="47.5" width="45" height="45" filterUnits="userSpaceOnUse"
        color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix" />
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="2.5" />
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_13787_125880" />
        <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_13787_125880" result="shape" />
      </filter>
      <linearGradient id="paint0_linear_13787_125880" x1="21.0204" y1="30.8634" x2="64.9643" y2="72.1439"
        gradientUnits="userSpaceOnUse">
        <stop stop-color="#D0D5DD" />
        <stop offset="0.350715" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient id="paint1_linear_13787_125880" x1="47.3204" y1="20.5421" x2="102.25" y2="72.1428"
        gradientUnits="userSpaceOnUse">
        <stop stop-color="#D0D5DD" />
        <stop offset="0.350715" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient id="paint2_linear_13787_125880" x1="85.5491" y1="36.0201" x2="124" y2="72.1406"
        gradientUnits="userSpaceOnUse">
        <stop stop-color="#D0D5DD" />
        <stop offset="0.350715" stop-color="white" stop-opacity="0" />
      </linearGradient>
    </defs>
  </svg>
</template>

<style scoped></style>