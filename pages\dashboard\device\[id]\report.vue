<template>
  <div class="h-screen overflow-auto">
    <div class="p-6 relative">
    <div v-if="isLoading" class="absolute inset-0 bg-white/80 z-50 flex items-center justify-center">
      <div class="text-center">
        <div class="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
        <p class="text-gray-600">Generating report...</p>
      </div>
    </div>
    <div class="mb-6">
      <h1 class="text-2xl font-bold mb-2">Device History Report</h1>
      <div class="lg:w-1/2 pt-4">
        <general-range-date-picker v-model="dateRange" id="filterDateRange" :position="isMobile ? 'center' : 'left'"
                                   label="Time & Date Range" placeholder="Choose Date"/>
      </div>
    </div>

    <div v-for="(group, index) in historyGroups" :key="index" class="mb-8">
      <div class="bg-gray-100 p-4 rounded-lg mb-4">
        <h3 class="font-bold mb-2">Voyage Summary</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <p class="text-gray-600">Voyage Duration</p>
            <p>{{ group.time || 'N/A' }}</p>
          </div>
          <div>
            <p class="text-gray-600">Distance Traveled</p>
            <p>{{ formatNauticalMiles(group.distance) }}</p>
          </div>
          <div v-if="group.top_speed">
            <p class="text-gray-600">Maximum Speed</p>
            <p>{{ (group.top_speed).toFixed(1) }} knots</p>
          </div>
          <div v-if="group.average_speed">
            <p class="text-gray-600">Average Speed</p>
            <p>{{ (group.average_speed).toFixed(1) }} knots</p>
          </div>
          <div v-if="group.engine_work">
            <p class="text-gray-600">Engine Hours</p>
            <p>{{ formatDuration(group.engine_work) }}</p>
          </div>
          <div v-if="getAverageInfo(group.items, 'fuellevel')">
            <p class="text-gray-600">Average Fuel Level</p>
            <p>{{ getAverageInfo(group.items, 'fuellevel') }}%</p>
          </div>
          <div v-if="getStartEndPositions(group.items).start">
            <p class="text-gray-600">Departure Position</p>
            <p class="text-sm">{{ formatCoordinates(...getStartEndPositions(group.items).start) }}</p>
          </div>
          <div v-if="getStartEndPositions(group.items).end">
            <p class="text-gray-600">Current Position</p>
            <p class="text-sm">{{ formatCoordinates(...getStartEndPositions(group.items).end) }}</p>
          </div>
        </div>
      </div>

      <general-data-table
          :headers="headers"
          :items="group.items"
          :is-loading="isLoading"
          :is-scrollable="true"
          empty-title="No Voyage Data"
          empty-subtitle="Select a date range and generate report to see vessel history."
      >
        <template #item.time="{ item }">
          {{ new Date(item.time).toLocaleString() }}
        </template>
        <template #item.speed="{ item }">
          {{ (item.speed).toFixed(1) }} knots
        </template>
        <template #item.coordinates="{ item }">
          {{ formatCoordinates(item.latitude, item.longitude) }}
        </template>
        <template #item.distance="{ item }">
          {{ formatNauticalMiles(item.distance) }}
        </template>
        <template #item.heading="{ item }">
          {{ getCompassDirection(item.course) }} ({{ item.course }}°)
        </template>
        <template #item.marine_info="{ item }">
          <div class="overflow-x-auto max-w-[250px]">
            <div class="whitespace-nowrap">
              <div v-if="getOtherInfo(item).enginehours" class="inline-block mr-2 px-2 py-1 bg-blue-100 rounded">
                Engine: {{ getOtherInfo(item).enginehours }}h
              </div>
              <div v-if="getOtherInfo(item).fuellevel" class="inline-block mr-2 px-2 py-1 bg-green-100 rounded">
                Fuel: {{ getOtherInfo(item).fuellevel }}%
              </div>
              <div v-if="getOtherInfo(item).motion" class="inline-block mr-2 px-2 py-1 bg-yellow-100 rounded">
                {{ getOtherInfo(item).motion === 'true' ? 'Underway' : 'At Anchor' }}
              </div>
              <div v-if="getOtherInfo(item).storageload" class="inline-block mr-2 px-2 py-1 bg-purple-100 rounded">
                Load: {{ getOtherInfo(item).storageload }}%
              </div>
              <div v-if="getOtherInfo(item).storagetemp" class="inline-block mr-2 px-2 py-1 bg-red-100 rounded">
                Storage Temp: {{ getOtherInfo(item).storagetemp }}°C
              </div>
            </div>
          </div>
        </template>
        <template #item.sensors="{ item }">
          <div class="overflow-x-auto max-w-[200px]">
            <div class="whitespace-nowrap">
              <div v-for="sensor in item.sensors_data" :key="sensor.id"
                   class="text-sm inline-block mr-2 px-2 py-1 bg-gray-100 rounded">
                <span class="font-medium">{{ sensor.id }}:</span> {{ sensor.value }}
              </div>
            </div>
          </div>
        </template>
        <template #item.actions="{ item }">
          <Button
              variant="secondary"
              size="sm"
              @click="showRawData(item)"
          >
            Show Raw
          </Button>
        </template>
      </general-data-table>
    </div>

    <general-modal
        id="raw-data-modal"
        :title="'Raw Data'"
        :is-has-close="true"
        class-modal="max-w-2xl"
    >
      <template #body>
        <pre class="bg-gray-100 p-4 rounded overflow-auto max-h-96">{{ JSON.stringify(selectedItem, null, 2) }}</pre>
      </template>
    </general-modal>
  </div>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import type {TelematicsHistoryGroup, TelematicsHistoryItem} from '~/types/history'
import {useModal} from '~/composables/modal'
import {formatDate} from "date-fns";

const route = useRoute()
const deviceId = route.params.id
const isLoading = ref(false)
const historyGroups = ref<TelematicsHistoryGroup[]>([])
const selectedItem = ref<TelematicsHistoryItem | null>(null)

function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  return `${hours}h ${minutes}m`
}

const today = new Date()
const startOfDay = new Date(today)
startOfDay.setHours(0, 0, 0, 0)
const endOfDay = new Date(today);
endOfDay.setHours(23, 59, 59, 999)

const dateRange = ref<Date[]>([startOfDay, endOfDay])

const fromDate = ref(new Date().toISOString().split('T')[0])
const fromTime = ref('00:00')
const toDate = ref(new Date().toISOString().split('T')[0])
const toTime = ref('23:59')

const headers = [
  {text: 'Time', value: 'time'},
  {text: 'Speed (knots)', value: 'speed'},
  {text: 'Position', value: 'coordinates'},
  {text: 'Distance (nm)', value: 'distance'},
  {text: 'Heading', value: 'heading'},
  {text: 'Vessel Status', value: 'marine_info', width: '250px'},
  {text: 'Sensors', value: 'sensors', width: '200px'},
  {text: 'Actions', value: 'actions'}
]

function formatCoordinates(lat: number, lng: number): string {
  const latDir = lat >= 0 ? 'N' : 'S'
  const lngDir = lng >= 0 ? 'E' : 'W'
  return `${Math.abs(lat).toFixed(4)}°${latDir}, ${Math.abs(lng).toFixed(4)}°${lngDir}`
}

function formatNauticalMiles(km: number): string {
  const nm = km * 0.539957
  return nm < 0.1 ? `${(nm * 6076.12).toFixed(0)} yards` : `${nm.toFixed(1)} nm`
}

function getStartEndPositions(items: TelematicsHistoryItem[]) {
  if (!items.length) return {start: null, end: null}
  const start = [items[0].latitude, items[0].longitude]
  const end = [items[items.length - 1].latitude, items[items.length - 1].longitude]
  return {start, end}
}

function getCompassDirection(degrees: number): string {
  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW']
  const index = Math.round(degrees / 22.5) % 16
  return directions[index]
}

function getAverageInfo(items: TelematicsHistoryItem[], key: string): number | null {
  const values = items
      .map(item => getOtherInfo(item))
      .filter(info => info[key])
      .map(info => parseFloat(info[key]))
      .filter(value => !isNaN(value))

  if (values.length === 0) return null
  return Math.round(values.reduce((a, b) => a + b, 0) / values.length)
}

function showRawData(item: TelematicsHistoryItem) {
  selectedItem.value = item
  const modal = document.getElementById('raw-data-modal')
  if (modal) {
    modal.classList.remove('hidden')
    modal.classList.add('animate-fade-in')
  }
}

function getOtherInfo(item: TelematicsHistoryItem) {
  const info: Record<string, any> = {}
  if (item.other_arr) {
    item.other_arr.forEach(str => {
      const [key, value] = str.split(': ')
      info[key.toLowerCase()] = value
    })
  }
  return info
}

async function generateReport() {
  isLoading.value = true
  historyGroups.value = [] // Clear previous data
  try {
    const response = await $fetch(`/api/telematics/history`, {
      params: {
        device_id: deviceId,
        from_date: formatDate(dateRange.value[0], 'yyyy-MM-dd'),
        from_time: formatDate(dateRange.value[0], 'HH:mm'),
        to_date: formatDate(dateRange.value[1], 'yyyy-MM-dd'),
        to_time: formatDate(dateRange.value[1], 'HH:mm'),
        snap_to_road: false
      }
    })
    historyGroups.value = response.items
  } catch (error) {
    console.error('Failed to fetch history:', error)
  } finally {
    isLoading.value = false
  }
}

watch(() => dateRange.value, () => {
  generateReport()
}, {deep: true})


const isMobile = computed(() => {
  return window.innerWidth <= 640
})

onMounted(() => {
  generateReport()
})
</script>

<style scoped>
.max-h-96 {
  max-height: 24rem;
}

.overflow-x-auto {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}
</style>