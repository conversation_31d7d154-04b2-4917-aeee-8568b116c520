<script setup lang="ts">
import {useRoute} from "#app";

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  destination: {
    type: String,
    default: '#'
  },
  name: {
    type: String,
    default: ''
  },
  isShowName: {
    type: Boolean,
    default: false
  }
})

const linkClass = computed((): string => {
  const isActive = useRoute().path.startsWith(props.destination)
  const hover = isActive ? 'bg-white text-black' : 'hover:bg-gray-100  hover:text-black text-white'
  return `py-2 ${props.isShowName ? 'px-3' : 'px-2'} group space-x-3 flex items-center rounded-md transition-all duration-300 ease-in-out ${hover}`
})
</script>

<template>
  <nuxt-link
      :id="props.id"
      :to="props.destination"
      :class="linkClass"
  >
    <slot name="icon"/>
    <span v-if="isShowName" class="font-medium text-base animate-fade-in">{{ props.name }}</span>
  </nuxt-link>
</template>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fade-in var(--fade-duration, 0.8s) ease-in-out;
}
</style>
