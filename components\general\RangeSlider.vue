<template>
  <div class="relative h-4">
    <div class="relative h-4">
      <!-- Single thumb mode -->
      <template v-if="isSingle">
        <input
          :value="singleValue"
          :disabled="disabled"
          :max="max"
          :min="min"
          :step="step"
          class="absolute top-0 left-0 w-full opacity-0 cursor-pointer"
          type="range"
          @input="updateValue($event.target.value)"
        />
        <div class="absolute top-1/2 left-0 w-full h-2 bg-gray-200 rounded-lg -translate-y-1/2"></div>
        <div
          :style="{ width: position + '%' }"
          class="absolute top-1/2 h-2 bg-blue-600 rounded-lg -translate-y-1/2"
        ></div>
        <div
          :style="{ left: position + '%' }"
          class="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-blue-500 rounded-full"
        ></div>
      </template>

      <!-- Dual thumb mode -->
      <template v-else>
        <input
          v-for="(thumb, index) in thumbs"
          :key="index"
          v-model="thumb.value"
          :disabled="disabled"
          :max="max"
          :min="min"
          :step="step"
          class="absolute top-0 left-0 w-full opacity-0 cursor-pointer"
          type="range"
          @input="updateThumbs(index, $event.target.value)"
        />
        <div class="absolute top-1/2 left-0 w-full h-2 bg-gray-200 rounded-lg -translate-y-1/2"></div>
        <div
          :style="{ left: thumbs[0].position + '%', width: (thumbs[1].position - thumbs[0].position) + 1 + '%' }"
          class="absolute top-1/2 h-2 bg-primary-500 rounded-lg -translate-y-1/2"
        ></div>
        <div
          v-for="(thumb, index) in thumbs"
          :key="index"
          :style="{ left: thumb.position + '%' }"
          class="absolute top-1/2 -translate-y-1/2 w-4 h-4 bg-primary-500 rounded-full"
        ></div>
      </template>
    </div>
    <div
      v-if="!isSingle"
      class="absolute top-0 left-0 right-0 h-full"
      @mousedown="startDragging"
      @mouseleave="stopDragging"
      @mousemove="dragThumb"
      @mouseup="stopDragging"
      @touchend="stopDragging"
      @touchmove="dragThumb"
      @touchstart="startDragging"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch, PropType } from 'vue';

const props = defineProps({
  modelValue: {
    type: [Number, Array] as PropType<number | number[]>,
    required: true
  },
  disabled: {
    type: Boolean,
    default: false
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 100
  },
  step: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits(['update:modelValue']);

const isSingle = computed(() => typeof props.modelValue === 'number');
const singleValue = computed(() => isSingle.value ? props.modelValue as number : 0);

const position = computed(() => {
  const value = isSingle.value ? singleValue.value : (props.modelValue as number[])[0];
  return ((value - props.min) / (props.max - props.min)) * 100;
});

const thumbs = ref([
  { value: 0, position: 0 },
  { value: 0, position: 0 },
]);

watch(() => props.modelValue, (newValue) => {
  if (!isSingle.value) {
    const values = newValue as number[];
    thumbs.value[0].value = values[0];
    thumbs.value[1].value = values[1];
    updateThumbPositions();
  }
}, { immediate: true });

function updateValue(value: string) {
  const newValue = Math.min(Math.max(parseFloat(value), props.min), props.max);
  emit('update:modelValue', newValue);
}

let isDragging = false;
let activeThumb: number | null = null;

function updateThumbPositions() {
  const range = props.max - props.min;
  thumbs.value.forEach((thumb) => {
    thumb.position = ((thumb.value - props.min) / range) * 100;
  });
}

function updateThumbs(index: number, value: string) {
  const newValue = Math.min(Math.max(parseFloat(value), props.min), props.max);
  thumbs.value[index].value = newValue;
  updateThumbPositions();

  emit('update:modelValue', thumbs.value.map(thumb => thumb.value));
}

function startDragging(event: MouseEvent | TouchEvent) {
  if (props.disabled) return;

  isDragging = true;
  const clickPosition = event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
  const sliderRect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const sliderWidth = sliderRect.width;
  const clickPositionPercentage = ((clickPosition - sliderRect.left) / sliderWidth) * 100;

  activeThumb = thumbs.value.reduce((closest, thumb, index) => {
    const distance = Math.abs(thumb.position - clickPositionPercentage);
    return distance < Math.abs(closest.position - clickPositionPercentage) ? {
      index,
      position: thumb.position
    } : closest;
  }, { index: -1, position: Infinity }).index;
}

function dragThumb(event: MouseEvent | TouchEvent) {
  if (!isDragging || activeThumb === null || props.disabled) return;

  const dragPosition = event instanceof MouseEvent ? event.clientX : event.touches[0].clientX;
  const sliderRect = (event.currentTarget as HTMLElement).getBoundingClientRect();
  const sliderWidth = sliderRect.width;
  const dragPositionPercentage = ((dragPosition - sliderRect.left) / sliderWidth) * 100;

  const minPosition = activeThumb === 0 ? 0 : thumbs.value[activeThumb - 1].position;
  const maxPosition = activeThumb === thumbs.value.length - 1 ? 100 : thumbs.value[activeThumb + 1].position;
  const newPosition = Math.min(Math.max(dragPositionPercentage, minPosition), maxPosition);

  const range = props.max - props.min;
  const newValue = Math.round(((newPosition / 100) * range) + props.min);
  updateThumbs(activeThumb, String(newValue));
}

function stopDragging() {
  isDragging = false;
  activeThumb = null;
}
</script>
