<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21.5625 16.3125V17.25C21.5625 17.7675 21.1425 18.1875 20.625 18.1875H18.9375V16.3125H21.5625ZM20.625 17.4375C20.8687 17.4375 20.8687 17.0625 20.625 17.0625H19.875C19.6313 17.0625 19.6313 17.4375 19.875 17.4375H20.625Z" fill="#0D0000"/>
    <path d="M21.5625 6V15.9375H19.6875V5.0625H20.625C21.1425 5.0625 21.5625 5.4825 21.5625 6Z" fill="#0D0000"/>
    <path d="M19.3125 3V15.9375H4.6875V3C4.6875 2.68875 4.93875 2.4375 5.25 2.4375H18.75C19.0612 2.4375 19.3125 2.68875 19.3125 3ZM18.1875 14.8125V11.0625H15.1875V14.8125H18.1875ZM18 3.9375C18.2437 3.9375 18.2437 3.5625 18 3.5625H15.375C15.1313 3.5625 15.1313 3.9375 15.375 3.9375H18ZM18 5.4375C18.2437 5.4375 18.2437 5.0625 18 5.0625H15.375C15.1313 5.0625 15.1313 5.4375 15.375 5.4375H18ZM18 6.9375C18.2437 6.9375 18.2437 6.5625 18 6.5625H15.375C15.1313 6.5625 15.1313 6.9375 15.375 6.9375H18ZM18 8.4375C18.2437 8.4375 18.2437 8.0625 18 8.0625H15.375C15.1313 8.0625 15.1313 8.4375 15.375 8.4375H18ZM18 9.9375C18.2437 9.9375 18.2437 9.5625 18 9.5625H15.375C15.1313 9.5625 15.1313 9.9375 15.375 9.9375H18ZM14.4338 8.4375C14.5425 5.74125 12.2588 3.46125 9.5625 3.56625V4.31625C8.05125 4.3875 6.6825 5.355 6.10875 6.75375C5.50875 8.205 5.85375 9.91875 6.96 11.0287C8.04037 12.1125 9.69788 12.4762 11.1341 11.9362C12.5929 11.3887 13.6091 9.99 13.6841 8.4375H14.4338ZM13.5 14.8125C13.7437 14.8125 13.7437 14.4375 13.5 14.4375H6C5.75625 14.4375 5.75625 14.8125 6 14.8125H13.5ZM11.955 13.3125C12.195 13.3125 12.195 12.9375 11.955 12.9375H7.545C7.305 12.9375 7.305 13.3125 7.545 13.3125H11.955Z" fill="#0D0000"/>
    <path d="M18.5625 16.3125V18.1875H3.375C2.8575 18.1875 2.4375 17.7675 2.4375 17.25V16.3125H18.5625Z" fill="#0D0000"/>
    <path d="M6.1875 20.4375H17.8125V21.5625H6.1875V20.4375Z" fill="#0D0000"/>
    <path d="M15.5625 11.4375H17.8125V14.4375H15.5625V11.4375Z" fill="#0D0000"/>
    <path d="M8.8125 18.5625H15.1875V20.0625H8.8125V18.5625Z" fill="#0D0000"/>
    <path d="M14.0588 8.06249H13.3838L13.9872 7.45874C14.025 7.65749 14.0513 7.85999 14.0588 8.06249Z" fill="#0D0000"/>
    <path d="M13.8864 7.03118L12.8552 8.06243H11.7939L13.5827 6.27368C13.7064 6.51743 13.8114 6.76868 13.8864 7.03118Z" fill="#0D0000"/>
    <path d="M13.3916 5.93618L11.2654 8.06243H10.2041L12.9307 5.33618C13.0991 5.52368 13.2529 5.72243 13.3916 5.93618Z" fill="#0D0000"/>
    <path d="M9.56227 8.43741H13.3085C13.2335 9.82491 12.3298 11.0737 11.0323 11.5724C9.72764 12.0787 8.21227 11.7524 7.22602 10.7624C6.23602 9.77241 5.92139 8.24616 6.43514 6.94491C6.94139 5.65866 8.18264 4.76616 9.56264 4.69116L9.56227 8.43741Z" fill="#0D0000"/>
    <path d="M12.6638 5.06989L9.9375 7.79614V6.73489L12.0637 4.60864C12.2775 4.74739 12.4763 4.90114 12.6638 5.06989Z" fill="#0D0000"/>
    <path d="M11.7263 4.41752L9.9375 6.20627V5.14502L10.9688 4.11377C11.2312 4.18877 11.4825 4.29377 11.7263 4.41752Z" fill="#0D0000"/>
    <path d="M10.5413 4.01241L9.9375 4.61616V3.94116C10.14 3.94866 10.3425 3.97491 10.5413 4.01241Z" fill="#0D0000"/>
    <path d="M4.3125 5.0625V15.9375H2.4375V6C2.4375 5.4825 2.8575 5.0625 3.375 5.0625H4.3125Z" fill="#0D0000"/>
  </svg>
</template>

<style scoped>

</style>
