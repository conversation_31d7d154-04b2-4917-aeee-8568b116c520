export interface TraccarDevice {
	id: number;
	name: string;
	uniqueId: string;
	status: string;
	disabled: boolean;
	lastUpdate: string | null;
	positionId: number;
	groupId: number;
	phone: string;
	model: string | null;
	contact: string | null;
	category: string;
	attributes: Record<string, any>;
}

export interface TelematicsDeviceData {
	id: number;
	user_id: number;
	current_driver_id: number | null;
	current_driver_rfid: string | null;
	timezone_id: number | null;
	traccar_device_id: number;
	icon_id: number;
	model_id: number | null;
	icon_colors: {
		moving: string;
		stopped: string;
		offline: string;
		engine: string;
		blocked: string;
	};
	active: number;
	kind: number;
	deleted: number;
	name: string;
	imei: string;
	fuel_measurement_id: number;
	fuel_quantity: string;
	fuel_price: string;
	fuel_per_km: string;
	fuel_per_h: string;
	sim_number: string;
	msisdn: string | null;
	device_model: string;
	plate_number: string;
	vin: string;
	registration_number: string;
	object_owner: string;
	additional_notes: string;
	authentication: string | null;
	comment: string;
	expiration_date: string | null;
	sim_expiration_date: string;
	sim_activation_date: string;
	installation_date: string;
	tail_color: string;
	tail_length: number;
	engine_hours: string;
	detect_engine: string;
	detect_speed: string;
	detect_distance: string | null;
	min_moving_speed: number;
	min_fuel_fillings: number;
	min_fuel_thefts: number;
	snap_to_road: number;
	gprs_templates_only: number;
	valid_by_avg_speed: number;
	max_speed: number | null;
	parameters: string;
	currents: string | null;
	created_at: string;
	updated_at: string;
	forward: string | null;
	device_type_id: number | null;
	app_tracker_login: number;
	fuel_detect_sec_after_stop: number;
	lbs: string | null;
	users: Array<{
		id: number;
		email: string;
	}>;
	pivot: {
		user_id: number;
		device_id: number;
		group_id: number;
		active: number;
		current_driver_id: number | null;
		timezone_id: number | null;
	};
	icon: {
		id: number;
		user_id: number | null;
		type: string;
		order: number | null;
		width: number;
		height: number;
		path: string;
		by_status: number;
	};
	traccar: {
		id: number;
		name: string;
		uniqueId: string;
		latestPosition_id: number;
		lastValidLatitude: number;
		lastValidLongitude: number;
		other: string;
		speed: string;
		time: string;
		device_time: string;
		server_time: string;
		ack_time: string;
		altitude: number;
		course: number;
		power: number | null;
		address: string | null;
		protocol: string;
		latest_positions: string;
		moved_at: string;
		stoped_at: string;
		move_begin_at: string;
		stop_begin_at: string;
		parked_end_at: string;
		engine_on_at: string;
		engine_off_at: string;
		engine_changed_at: string;
		updated_at: string;
		database_id: number | null;
	};
	lastValidLatitude: number;
	lastValidLongitude: number;
	latest_positions: string;
	icon_type: string;
	enable: number;
	group_id: number;
	user_timezone_id: number | null;
	time: string;
	course: number;
	speed: number;
}

export interface TelematicsDevice {
	id: number;
	alarm: number;
	name: string;
	online: string;
	time: string;
	timestamp: number;
	acktimestamp: number;
	lat: number;
	lng: number;
	course: number;
	speed: number;
	altitude: number;
	icon_type: string;
	icon_color: string;
	groupId?: number;
	sensors: Array<{
		id: number;
		type: string;
		name: string;
		value: string;
		val: string | number;
	}>;
	tail: Array<{ lat: string; lng: string }>;
	device_data: TelematicsDeviceData;
}

export interface MDVRDevice {
	deviceno: string;
	devicename: string;
	fleetname: string;
	plateno: string;
	vehicletype: string;
	devicetype: string;
	sim: string;
	chassisno: string | null;
	createtime: string;
	installDate: string;
	lastonlinetime: string;
	lastofflinetime: string;
	guid: string;
	channelname: string;
	longitude: number;
	latitude: number;
	altitude: number;
	speed: number;
	precision: number;
	direct: number;
	satellites: number;
	mode: number;
	dtu: string;
	todaymileage: number;
	totalmileage: number;
	deviceModel: string;
	appVersion: string;
	lastStatusJson: string;
	streamserver: string;
}

export interface UnifiedDevice {
	id: number;
	name: string;
	groupId?: number;
	status: string;
	telematics?: TelematicsDevice;
	traccar?: TraccarDevice;
	mdvr?: MDVRDevice;
	lat: number;
	lng: number;
	speed: number;
	altitude: number;
	course: number;
	lastUpdate?: string;
	source: 'traccar' | 'telematics' | 'mdvr';
}