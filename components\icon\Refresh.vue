<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 21 20" fill="none">
    <path d="M17.9998 8.33333C17.9998 8.33333 16.329 6.05685 14.9716 4.69854C13.6143 3.34022 11.7385 2.5 9.6665 2.5C5.52437 2.5 2.1665 5.85786 2.1665 10C2.1665 14.1421 5.52437 17.5 9.6665 17.5C13.0857 17.5 15.9706 15.2119 16.8734 12.0833M17.9998 8.33333V3.33333M17.9998 8.33333H12.9998" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>