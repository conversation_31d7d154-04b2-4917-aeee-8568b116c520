<script setup lang="ts">
interface RoundedType {
  type: 'full' | 'lg' | 'md' | 'sm'
}
const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  rounded: {
    type: String as () => 'full' | 'lg' | 'md' | 'none',
    default: 'lg'
  },
  color: {
    type: String,
    default: 'gray'
  },
  bordered: {
    type: Boolean,
    default: true
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['on-click'])

const buttonClass = (): string => {
  const rounded = `rounded-${props.rounded}`

  const color = props.disabled
    ? `border bg-gray-100 stroke-gray-600 border-gray-300 cursor-not-allowed`
    : (props.bordered
      ? `bg-white border border-${props.color}-300 stroke-${props.color}-600 hover:bg-${props.color}-100`
      : `bg-${props.color}-500 hover:bg-${props.color}-400`)

  return `h-12 flex items-center justify-center aspect-square transition ${rounded} ${color}`
}
</script>

<template>
  <button :id="props.id" :disabled="props.disabled" type="button" :class="buttonClass()" @click="emit('on-click')">
    <slot name="icon" />
  </button>
</template>

<style scoped> </style>