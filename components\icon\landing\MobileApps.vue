<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11.52 3.075H12.48C12.792 3.075 12.792 2.55 12.48 2.55H11.52C11.208 2.55 11.208 3.075 11.52 3.075ZM18 4.125H6V2.2875C6 1.85175 6.3216 1.5 6.72 1.5H17.28C17.6784 1.5 18 1.85175 18 2.2875V4.125Z" fill="#0D0000"/>
    <path d="M10.4256 5.54032L7.032 8.96129C6.8112 9.17903 7.152 9.52258 7.368 9.3L10.7616 5.87903C10.9824 5.66129 10.6416 5.31774 10.4256 5.54032ZM8.7264 5.87903C8.9472 5.66129 8.6064 5.31774 8.3904 5.54032L7.032 6.90484C6.8112 7.12742 7.152 7.47097 7.368 7.24839L8.7264 5.87903ZM6 4.5H18V19.5H6V4.5Z" fill="#0D0000"/>
    <path d="M11.04 21.45H12.96C13.272 21.45 13.272 20.925 12.96 20.925H11.04C10.728 20.925 10.728 21.45 11.04 21.45ZM17.28 22.5H6.72C6.3216 22.5 6 22.1482 6 21.7125V19.875H18V21.7125C18 22.1482 17.6784 22.5 17.28 22.5Z" fill="#0D0000"/>
  </svg>
</template>

<style scoped>

</style>
