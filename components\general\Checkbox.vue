<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: [<PERSON>olean, String],
    default: false
  },
  id: {
    type: String,
    default: () => `checkbox-${Math.random().toString(36).substr(2, 9)}`
  },
  value: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: 'gray'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  returnType: {
    type: String as () => 'boolean' | 'string',
    default: 'boolean'
  }
})

const emit = defineEmits(['update:model-value'])

const inputClass = () => {
  const baseClass = 'rounded border-gray-300 focus:ring-transparent'

  const color = props.disabled
    ? 'bg-gray-100'
    : `text-${props.color}-500`

  const cursor = props.disabled
    ? 'cursor-not-allowed'
    : props.readonly ? '' : 'cursor-pointer'

  return `${baseClass} ${cursor} ${color}`
}

const labelClass = () => {
  const baseClass = 'ml-2 text-sm font-medium truncate select-none'

  const color = 'text-gray-700'

  const cursor = props.disabled
    ? 'cursor-not-allowed'
    : props.readonly ? '' : 'cursor-pointer'

  return `${baseClass} ${cursor} ${color}`
}

function onChange(isChecked: boolean) {
  switch (props.returnType) {
    case 'string': emit('update:model-value', isChecked ? props.value : null); break
    case 'boolean': emit('update:model-value', isChecked); break
  }
}
</script>

<template>
  <label :for="id" class="flex items-center" :class="props.disabled ? 'cursor-not-allowed' : 'cursor-pointer'">
    <input 
      :id="id"
      :checked="Boolean(props.modelValue)" 
      :name="props.name" 
      :disabled="props.disabled || props.readonly"
      :readonly="props.readonly" 
      type="checkbox" 
      :class="inputClass()" 
      @change="onChange($event.target?.checked)"
    >
    <span :class="labelClass()">{{ props.label }}</span>
  </label>
</template>

<style scoped></style>
