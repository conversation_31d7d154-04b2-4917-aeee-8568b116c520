<script setup lang="ts">
import {Collapse, initFlowbite} from "flowbite";
import type {CollapseInterface, CollapseOptions} from "flowbite";

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  triggerClass: {
    type: String,
    default: ''
  },
  isHorizontalIcon: {
    type: Boolean,
    default: false
  }
})

let collapse: CollapseInterface | null = null
const isVisible = ref(false)
const targetId = ref<string>(`target${props.id[0].toUpperCase() + props.id?.substring(1)}`)

onMounted(() => {
  initFlowbite()

  const $trigger = document.getElementById(props.id)
  const $target = document.getElementById(targetId.value)

  const options: CollapseOptions  = {
    onToggle: (collapse: CollapseInterface) => {
      isVisible.value = collapse._visible
    }
  }

  collapse = new Collapse($target, $trigger, options)
})

const buttonClass = computed(() => {
  const baseClass = 'space-x-2 w-full cursor-pointer transition flex items-center justify-between font-medium select-none'
  return `${baseClass} ${props.triggerClass}`
})

const chevronClass = computed(() => {
  const baseClass = 'transition-transform stroke-gray-900'
  const rotate = isVisible.value ? 'rotate-180' : 'rotate-0'
  return `${baseClass} ${rotate}`
})
</script>

<template>
  <div>
    <p
      type="button"
      :id="props.id"
      :aria-controls="targetId"
      :data-collapse-toggle="targetId"
      :class="buttonClass"
    >
      <span :class="isHorizontalIcon ? 'flex items-center gap-2 w-full':'items-center gap-2 w-full'">
        <slot name="icon"/>
        <div class="flex items-center justify-between w-full">
          <slot name="label">
            <span>{{ props.label }}</span>
          </slot>
        </div>
      </span>

      <icon-chevron-down
        size="20"
        :class="chevronClass"
      />
    </p>

    <div :id="targetId" class="hidden mt-6 space-y-2">
      <slot name="content"/>
    </div>
  </div>
</template>

<style scoped>

</style>
