<template>
	<div class="bg-white rounded-lg shadow-lg w-[300px] absolute bottom-4 right-24 z-[100]">
		<!-- Header -->
		<div class="flex items-center justify-between p-4 cursor-pointer border-b border-gray-200" @click="toggleExpand">
			<h2 class="text-lg font-semibold">Windy Controls</h2>
			<icon-chevron-up class="w-5 h-5 stroke-gray-500 transform transition-transform duration-300"
				:class="[expanded ? 'rotate-180' : '']" />
		</div>

		<!-- Content -->
		<div v-if="expanded" class="p-4 space-y-4">
			<!-- Toggle Windy -->
			<div class="flex items-center justify-between">
				<span class="text-sm text-gray-700">Show Windy</span>
				<general-checkbox 
					:model-value="store.mapSettings.overlayLayers.showWindy"
					@update:model-value="store.toggleWindy"
					color="primary"
				/>
			</div>

			<!-- Settings -->
			<div v-if="store.mapSettings.overlayLayers.showWindy" class="space-y-4 pt-4 border-t border-gray-200">
				<!-- Product Selection -->
				<div class="space-y-2">
					<label class="text-sm text-gray-700">Product</label>
					<general-dropdown id="product-dropdown" class="w-full">
						<template #activator>
							<button class="w-full px-3 py-2 text-sm text-left bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
								{{ store.mapSettings.windySettings.product.toUpperCase() }}
							</button>
						</template>
						<template #content>
							<div class="w-full py-1 bg-white rounded-lg shadow-lg border border-gray-200">
								<button 
									v-for="product in ['ecmwf', 'gfs']" 
									:key="product"
									@click="store.mapSettings.windySettings.product = product"
									class="w-full px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors"
									:class="{'text-primary-600': store.mapSettings.windySettings.product === product}"
								>
									{{ product.toUpperCase() }}
								</button>
							</div>
						</template>
					</general-dropdown>
				</div>

				<!-- Overlay Selection -->
				<div class="space-y-2">
					<label class="text-sm text-gray-700">Overlay</label>
					<general-dropdown id="overlay-dropdown" class="w-full">
						<template #activator>
							<button class="w-full px-3 py-2 text-sm text-left bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
								{{ store.mapSettings.windySettings.overlay.charAt(0).toUpperCase() + store.mapSettings.windySettings.overlay.slice(1) }}
							</button>
						</template>
						<template #content>
							<div class="w-full py-1 bg-white rounded-lg shadow-lg border border-gray-200">
								<button 
									v-for="overlay in ['wind', 'temp', 'pressure', 'clouds', 'waves']" 
									:key="overlay"
									@click="store.mapSettings.windySettings.overlay = overlay"
									class="w-full px-4 py-2 text-sm text-left hover:bg-gray-50 transition-colors"
									:class="{'text-primary-600': store.mapSettings.windySettings.overlay === overlay}"
								>
									{{ overlay.charAt(0).toUpperCase() + overlay.slice(1) }}
								</button>
							</div>
						</template>
					</general-dropdown>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useMapStore } from '~/store/map'
import iconChevronUp from '~/components/icon/ChevronUp.vue'
import GeneralCheckbox from '~/components/general/Checkbox.vue'
import GeneralDropdown from '~/components/general/Dropdown.vue'

const store = useMapStore()
const expanded = ref(false)

const toggleExpand = () => {
	expanded.value = !expanded.value
}
</script>
