export const useProfile = () => {
  const { data } = useAuth()

  const initialProviders = computed(() => {
    if (!data.value) return []

    return [...data.value.initialProviders]
  })

  // TODO: Add isTraccar if necessary

  const isTelematics = computed(() => {
    return data.value && Boolean(data.value.integrationTokens.find(i => i.provider === 'telematics'))
  })

  const isMdvr = computed(() => {
    return data.value && Bo<PERSON>an(data.value.integrationTokens.find(i => i.provider === 'mdvr'))
  })

  return {
    initialProviders,
    isTelematics,
    isMdvr
  }
}