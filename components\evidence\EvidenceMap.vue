<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue'
import * as L from 'leaflet'
import 'leaflet/dist/leaflet.css'

const props = defineProps({
	latitude: {
		type: Number,
		required: true
	},
	longitude: {
		type: Number,
		required: true
	}
})

const mapInstance = ref<L.Map | null>(null)
const marker = ref<L.Marker | null>(null)

// Custom marker icon
const customIcon = L.divIcon({
	className: 'custom-marker',
	html: `<div class="w-6 h-6 bg-primary rounded-full border-2 border-white shadow-lg flex items-center justify-center">
		<div class="w-2 h-2 bg-white rounded-full"></div>
	</div>`,
	iconSize: [24, 24],
	iconAnchor: [12, 12]
})

const initMap = () => {
	if (!mapInstance.value) {
		mapInstance.value = L.map('evidence-map', {
			zoomControl: true,
			attributionControl: false,
			zoomAnimation: true,
			preferCanvas: true,
		}).setView([props.latitude, props.longitude], 15)

		<PERSON><PERSON>tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
			attribution: '&copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors'
		}).addTo(mapInstance.value)

		marker.value = L.marker([props.latitude, props.longitude], {
			icon: customIcon,
			title: 'Evidence Location'
		}).addTo(mapInstance.value)

		// Invalidate size after a short delay to ensure proper rendering
		setTimeout(() => {
			mapInstance.value?.invalidateSize()
		}, 300)
	}
}

watch(() => [props.latitude, props.longitude], ([newLat, newLng]) => {
	if (mapInstance.value && marker.value) {
		const latLng = L.latLng(newLat, newLng)
		marker.value.setLatLng(latLng)
		mapInstance.value.setView(latLng, mapInstance.value.getZoom())
	}
})

onMounted(() => {
	initMap()
})

onUnmounted(() => {
	if (mapInstance.value) {
		mapInstance.value.remove()
		mapInstance.value = null
	}
})
</script>

<template>
	<div>
		<div id="evidence-map" class="h-[300px] w-full rounded-lg overflow-hidden border border-gray-200 relative"></div>
	</div>
</template>

<style>
.leaflet-container {
	background: #f3f4f6 !important;
	width: 100% !important;
	height: 100% !important;
}

.custom-marker {
	display: flex;
	align-items: center;
	justify-center: center;
}

#evidence-map {
	z-index: 1;
	pointer-events: auto;
}

.leaflet-marker-icon {
	transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
	transform-origin: center center !important;
}
</style>