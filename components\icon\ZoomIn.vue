<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '20'
  }
})
</script>

<template>
  <svg :width="size" :height="size" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
        d="M8.33366 1.66699C7.01512 1.66699 5.72619 2.05799 4.62986 2.79053C3.53353 3.52307 2.67905 4.56426 2.17446 5.78244C1.66988 7.00061 1.53786 8.34105 1.79509 9.63426C2.05233 10.9275 2.68727 12.1154 3.61962 13.0477C4.55197 13.9801 5.73985 14.615 7.03306 14.8722C8.32627 15.1295 9.66671 14.9974 10.8849 14.4929C12.1031 13.9883 13.1442 13.1338 13.8768 12.0375C14.6093 10.9411 15.0003 9.6522 15.0003 8.33366C14.9983 6.56616 14.2953 4.87162 13.0455 3.62181C11.7957 2.37199 10.1012 1.66898 8.33366 1.66699ZM8.33366 13.3337C7.34475 13.3337 6.37806 13.0404 5.55581 12.491C4.73356 11.9416 4.0927 11.1607 3.71426 10.2471C3.33583 9.33345 3.23681 8.32811 3.42974 7.35821C3.62266 6.3883 4.09887 5.49739 4.79813 4.79813C5.49739 4.09886 6.38831 3.62266 7.35821 3.42973C8.32811 3.23681 9.33345 3.33582 10.2471 3.71426C11.1607 4.0927 11.9416 4.73356 12.491 5.55581C13.0404 6.37805 13.3337 7.34475 13.3337 8.33366C13.3323 9.65934 12.8051 10.9303 11.8677 11.8677C10.9303 12.8051 9.65934 13.3323 8.33366 13.3337Z"/>
    <path
        d="M18.0895 16.9112L14.7562 13.5778C14.599 13.426 14.3885 13.342 14.17 13.3439C13.9515 13.3458 13.7425 13.4335 13.588 13.588C13.4335 13.7425 13.3458 13.9515 13.3439 14.17C13.342 14.3885 13.426 14.599 13.5778 14.7562L16.9112 18.0895C17.0683 18.2413 17.2788 18.3253 17.4973 18.3234C17.7158 18.3215 17.9248 18.2338 18.0793 18.0793C18.2338 17.9248 18.3215 17.7158 18.3234 17.4973C18.3253 17.2788 18.2413 17.0683 18.0895 16.9112Z"/>
    <path
        d="M10.8337 7.50033H9.16699V5.83366C9.16699 5.61264 9.0792 5.40068 8.92292 5.2444C8.76664 5.08812 8.55468 5.00033 8.33366 5.00033C8.11265 5.00033 7.90069 5.08812 7.74441 5.2444C7.58813 5.40068 7.50033 5.61264 7.50033 5.83366V7.50033H5.83366C5.61265 7.50033 5.40069 7.58812 5.24441 7.7444C5.08813 7.90068 5.00033 8.11265 5.00033 8.33366C5.00033 8.55467 5.08813 8.76663 5.24441 8.92291C5.40069 9.07919 5.61265 9.16699 5.83366 9.16699H7.50033V10.8337C7.50033 11.0547 7.58813 11.2666 7.74441 11.4229C7.90069 11.5792 8.11265 11.667 8.33366 11.667C8.55468 11.667 8.76664 11.5792 8.92292 11.4229C9.0792 11.2666 9.16699 11.0547 9.16699 10.8337V9.16699H10.8337C11.0547 9.16699 11.2666 9.07919 11.4229 8.92291C11.5792 8.76663 11.667 8.55467 11.667 8.33366C11.667 8.11265 11.5792 7.90068 11.4229 7.7444C11.2666 7.58812 11.0547 7.50033 10.8337 7.50033Z"/>
  </svg>
</template>

<style scoped>

</style>
