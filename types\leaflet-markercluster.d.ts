import * as L from 'leaflet'

declare module 'leaflet' {
	interface MarkerClusterGroupOptions {
		maxClusterRadius?: number
		spiderfyOnMaxZoom?: boolean
		showCoverageOnHover?: boolean
		zoomToBoundsOnClick?: boolean
		singleMarkerMode?: boolean
		disableClusteringAtZoom?: number
		animate?: boolean
	}

	class MarkerClusterGroup extends L.FeatureGroup {
		constructor(options?: MarkerClusterGroupOptions)
		clearLayers(): this
		addLayer(layer: L.Layer): this
		addLayers(layers: L.Layer[]): this
		removeLayer(layer: L.Layer): this
		removeLayers(layers: L.Layer[]): this
		hasLayer(layer: L.Layer): boolean
		zoomToShowLayer(layer: L.Layer, callback?: () => void): void
	}
}

declare module 'leaflet.markercluster'