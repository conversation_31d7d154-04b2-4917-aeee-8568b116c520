<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Array as () => boolean[],
    default: []
  },
  id: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  totalCol: {
    type: Number,
    default: 1
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  readonly: {
    type: Boolean,
    default: false
  },
  items: {
    type: Array as () => {
      title?: null | string,
      subtitle?: null | string,
      label?: null | string,
      value: string
    }[],
    default: []
  }
})

const emit = defineEmits(['update:model-value'])

const checkboxValues = ref<boolean[]>([])

const itemClass = (i: number) => {
  const baseClass = 'flex items-center space-x-3 rounded-lg border p-4'

  const color = checkboxValues.value[i]
    ? props.disabled ? 'border-gray-300 bg-gray-50' : 'border-primary-500 bg-primary-50'
    : 'border-gray-300'

  const cursor = props.disabled
    ? 'cursor-not-allowed'
    : props.readonly ? 'cursor-default' : 'cursor-pointer'

  return `${baseClass} ${color} ${cursor}`
}

const checkboxClass = () => {
  const baseClass = 'rounded focus:ring-transparent'

  const color = props.disabled ? 'text-gray-300' : 'text-primary-500'

  const cursor = props.disabled
    ? 'cursor-not-allowed'
    : props.readonly ? 'cursor-default' : 'cursor-pointer'

  return `${baseClass} ${color} ${cursor}`
}

function onClickItem(i: number) {
  if (props.readonly || props.disabled) return

  checkboxValues.value[i] = !checkboxValues.value[i]
  emit('update:model-value', [...checkboxValues.value])
}

watch(() => props.modelValue, (value) => {
  checkboxValues.value = [...value]
}, { deep: true, immediate: true })
</script>

<template>
  <div>
    <p class="text-sm font-medium text-gray-700 cursor-default select-none mb-1.5">
      {{ props.label }}
      <span v-if="props.required" class="text-primary-500">*</span>
    </p>
    <div :class="`grid grid-cols-${props.totalCol} gap-3`">
      <div v-for="(item, i) in props.items" :class="itemClass(i)" @click="onClickItem(i)">
        <input type="checkbox" :id="`${props.id}${item.value}`" :name="props.id" :readonly="props.readonly"
          :disabled="props.disabled || props.readonly" :checked="checkboxValues[i]" :class="checkboxClass()" />
        <div class="space-y-0.5">
          <p v-if="item.title" class="font-semibold text-gray-900 select-none">{{ item.title }}</p>
          <p v-if="item.subtitle" class="text-sm text-gray-500 select-none">{{ item.subtitle }}</p>
          <p v-if="item.label" class="text-sm font-semibold select-none"
            :class="props.disabled ? 'text-gray-500' : 'text-gray-700'">{{ item.label }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>