{"id": "d5f5000a-14be-4b43-997e-ce0172dc57c8", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.telematics_user_data": {"name": "telematics_user_data", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "api_hash": {"name": "api_hash", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "integer", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"telematics_user_data_user_id_users_id_fk": {"name": "telematics_user_data_user_id_users_id_fk", "tableFrom": "telematics_user_data", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.traccar_user_data": {"name": "traccar_user_data", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": true, "notNull": true}, "traccar_id": {"name": "traccar_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "administrator": {"name": "administrator", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "readonly": {"name": "readonly", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "device_limit": {"name": "device_limit", "type": "integer", "primaryKey": false, "notNull": false, "default": -1}, "user_limit": {"name": "user_limit", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "device_readonly": {"name": "device_readonly", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "disabled": {"name": "disabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "attributes": {"name": "attributes", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"traccar_user_data_user_id_users_id_fk": {"name": "traccar_user_data_user_id_users_id_fk", "tableFrom": "traccar_user_data", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_integrations": {"name": "user_integrations", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_integrations_user_id_users_id_fk": {"name": "user_integrations_user_id_users_id_fk", "tableFrom": "user_integrations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_integrations_user_id_provider_pk": {"name": "user_integrations_user_id_provider_pk", "columns": ["user_id", "provider"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}