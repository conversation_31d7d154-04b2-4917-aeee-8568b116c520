<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.5812 20.0625H5.25C4.7325 20.0625 4.3125 19.6425 4.3125 19.125V17.0625H15.1875V19.125C15.1875 19.4775 15.33 19.815 15.5812 20.0625Z" fill="#0D0000"/>
    <path d="M7.5 3.9375H17.8312C17.58 4.185 17.4375 4.5225 17.4375 4.875V19.125C17.4375 20.3325 15.5625 20.3325 15.5625 19.125V16.6875H6.5625V4.875C6.5625 4.3575 6.9825 3.9375 7.5 3.9375ZM16.125 7.6875C16.3687 7.6875 16.3687 7.3125 16.125 7.3125H7.875C7.63125 7.3125 7.63125 7.6875 7.875 7.6875H16.125ZM16.125 9.1875C16.3687 9.1875 16.3687 8.8125 16.125 8.8125H7.875C7.63125 8.8125 7.63125 9.1875 7.875 9.1875H16.125ZM16.125 10.6875C16.3687 10.6875 16.3687 10.3125 16.125 10.3125H7.875C7.63125 10.3125 7.63125 10.6875 7.875 10.6875H16.125ZM16.125 12.1875C16.3687 12.1875 16.3687 11.8125 16.125 11.8125H7.875C7.63125 11.8125 7.63125 12.1875 7.875 12.1875H16.125ZM16.125 13.6875C16.3687 13.6875 16.3687 13.3125 16.125 13.3125H7.875C7.63125 13.3125 7.63125 13.6875 7.875 13.6875H16.125ZM16.125 15.1875C16.3687 15.1875 16.3687 14.8125 16.125 14.8125H7.875C7.63125 14.8125 7.63125 15.1875 7.875 15.1875H16.125ZM14.25 6.1875C14.4937 6.1875 14.4937 5.8125 14.25 5.8125H9.75C9.50625 5.8125 9.50625 6.1875 9.75 6.1875H14.25Z" fill="#0D0000"/>
    <path d="M19.6875 4.87511V6.93761H17.8125V4.87511C17.8125 3.66761 19.6875 3.66761 19.6875 4.87511Z" fill="#0D0000"/>
  </svg>
</template>

<style scoped>

</style>
