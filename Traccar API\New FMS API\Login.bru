meta {
  name: Login
  type: http
  seq: 1
}

post {
  url: http://localhost:3000/api/auth/signIn
  body: formUrlEncoded
  auth: none
}

headers {
  Accept-Language: en-US,en
  Connection: keep-alive
  Cookie: is_show_select_order_tooltip_cookie=true; JSESSIONID=node0hn39936mc96m13diobznlj68c801.node0; traccar.token=r1wn2uaxat8k35d9ypl69q
  Origin: http://localhost:3000
  Referer: http://localhost:3000/login
  Sec-Fetch-Dest: empty
  Sec-Fetch-Mode: cors
  Sec-Fetch-Site: same-origin
  Sec-GPC: 1
  User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
  accept: application/json
  content-type: application/x-www-form-urlencoded
  sec-ch-ua: "Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"
  sec-ch-ua-mobile: ?0
  sec-ch-ua-platform: "macOS"
}

body:form-urlencoded {
  email: <EMAIL>
  password: 1234qwer
}
