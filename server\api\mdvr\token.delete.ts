import { defineE<PERSON><PERSON><PERSON><PERSON> } from 'h3'
import { db } from '../../database/drizzle'
import { integrationTokens } from '../../database/schema'
import { eq, and } from 'drizzle-orm'
import { requireUserSession } from '~/server/utils/session'

export default defineEventHandler(async (event) => {
	const userId = getCookie(event, 'user_id')

	if (!userId) {
		throw createError({
			statusCode: 401,
			statusMessage: 'Unauthorized'
		})
	}

	// Delete the MDVR token for the user
	await db.delete(integrationTokens)
		.where(
			and(
				eq(integrationTokens.userId, userId),
				eq(integrationTokens.provider, 'mdvr')
			)
		)

	deleteCookie(event, 'mdvr.token')

	return {
		status: 'success',
		message: 'MDVR token deleted successfully'
	}
})