<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none">
    <path d="M17.2 14.0002C17.477 14.0002 17.6155 14.0002 17.7278 14.0617C17.8204 14.1124 17.9065 14.2077 17.9478 14.3049C17.9978 14.4227 17.9852 14.5481 17.96 14.7989C17.8296 16.0989 17.3822 17.3516 16.6518 18.4447C15.7727 19.7603 14.5233 20.7857 13.0615 21.3912C11.5997 21.9967 9.99113 22.1551 8.43928 21.8465C6.88743 21.5378 5.46197 20.7758 4.34315 19.657C3.22433 18.5382 2.4624 17.1127 2.15372 15.5609C1.84504 14.009 2.00347 12.4005 2.60897 10.9387C3.21447 9.47689 4.23985 8.22746 5.55544 7.34841C6.64856 6.61801 7.90125 6.17057 9.20131 6.04013C9.45207 6.01497 9.57745 6.00239 9.69528 6.05239C9.79249 6.09363 9.88776 6.17982 9.9385 6.27242C10 6.38467 10 6.52317 10 6.80017V13.2002C10 13.4802 10 13.6202 10.0545 13.7272C10.1024 13.8212 10.1789 13.8977 10.273 13.9457C10.38 14.0002 10.52 14.0002 10.8 14.0002H17.2Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M14 2.80017C14 2.52317 14 2.38466 14.0615 2.27241C14.1122 2.17981 14.2075 2.09362 14.3047 2.05238C14.4225 2.00238 14.5479 2.01496 14.7987 2.04011C16.6271 2.22351 18.346 3.03247 19.6569 4.34332C20.9677 5.65416 21.7767 7.37307 21.9601 9.20147C21.9852 9.45224 21.9978 9.57763 21.9478 9.69546C21.9066 9.79266 21.8204 9.88793 21.7278 9.93866C21.6155 10.0002 21.477 10.0002 21.2 10.0002L14.8 10.0002C14.52 10.0002 14.38 10.0002 14.273 9.94567C14.1789 9.89774 14.1024 9.82125 14.0545 9.72717C14 9.62021 14 9.4802 14 9.20017V2.80017Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>