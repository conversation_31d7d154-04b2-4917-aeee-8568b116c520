<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <div>
    <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M11.9988 8.00118V12.0012M11.9988 16.0012H12.0088M10.2888 2.86118L1.8188 17.0012C1.64417 17.3036 1.55177 17.6465 1.55079 17.9957C1.54981 18.3449 1.64029 18.6883 1.81323 18.9917C1.98616 19.2951 2.23553 19.5479 2.53651 19.725C2.83749 19.9021 3.1796 19.9973 3.5288 20.0012H20.4688C20.818 19.9973 21.1601 19.9021 21.4611 19.725C21.7621 19.5479 22.0114 19.2951 22.1844 18.9917C22.3573 18.6883 22.4478 18.3449 22.4468 17.9957C22.4458 17.6465 22.3534 17.3036 22.1788 17.0012L13.7088 2.86118C13.5305 2.56729 13.2795 2.3243 12.98 2.15567C12.6805 1.98703 12.3425 1.89844 11.9988 1.89844C11.6551 1.89844 11.3171 1.98703 11.0176 2.15567C10.7181 2.3243 10.4671 2.56729 10.2888 2.86118Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </div>
</template>

<style scoped>

</style>