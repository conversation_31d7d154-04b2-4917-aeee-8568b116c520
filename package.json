{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/image": "^1.9.0", "@nuxtjs/tailwindcss": "^6.12.2", "@pinia/nuxt": "^0.9.0", "@sidebase/nuxt-auth": "^0.10.0", "@vuepic/vue-datepicker": "^11.0.0", "date-fns": "^4.1.0", "drizzle-orm": "^0.38.3", "flowbite": "^2.5.2", "gsap": "^3.12.5", "leaflet": "^1.9.4", "leaflet-control-geocoder": "^2.4.0", "leaflet-rotatedmarker": "^0.2.0", "leaflet.markercluster": "^1.5.3", "nuxt": "^3.15.1", "nuxt3-leaflet": "^1.0.13", "pinia": "^2.3.0", "postgres": "^3.4.5", "vue": "latest", "vue-chartjs": "^5.3.2", "vue-router": "latest", "vue-virtual-scroller": "^2.0.0-beta.8", "vue3-toastify": "^0.2.8", "yup": "^1.6.1"}, "devDependencies": {"autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.1", "postcss": "^8.4.49", "tailwindcss": "^3.4.17"}}