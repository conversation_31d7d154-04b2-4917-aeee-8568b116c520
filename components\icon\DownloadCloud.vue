<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 20 20" fill="none">
    <g clip-path="url(#clip0_1160_13250)">
      <path d="M13.3335 17.5M16.9919 15.325C17.8047 14.8819 18.4467 14.1808 18.8168 13.3322C19.1868 12.4837 19.2637 11.5361 19.0354 10.6389C18.807 9.74182 18.2865 8.94629 17.5558 8.3779C16.8251 7.80951 15.9259 7.50064 15.0002 7.50003H13.9502C13.698 6.5244 13.2278 5.61864 12.5752 4.85085C11.9225 4.08307 11.1042 3.47324 10.182 3.0672C9.25967 2.66116 8.25734 2.46949 7.25031 2.5066C6.24328 2.5437 5.25777 2.80861 4.36786 3.28142C3.47795 3.75422 2.7068 4.42261 2.1124 5.23635C1.51799 6.05008 1.11579 6.98797 0.936028 7.97952C0.756269 8.97107 0.803632 9.99047 1.07456 10.9611C1.34548 11.9317 1.83291 12.8282 2.50021 13.5834" stroke-width="1.67" stroke-linecap="round" stroke-linejoin="round"/>
      <path d="M6.66736 14.1666L10.0007 17.5M10.0007 17.5L13.334 14.1666M10.0007 17.5V10" stroke-width="1.67" stroke-linecap="round" stroke-linejoin="round"/>

    </g>
    <defs>
      <clipPath id="clip0_1160_13250">
        <rect width="20" height="20" fill="white"/>
      </clipPath>
    </defs>
  </svg>
</template>

<style scoped>

</style>