import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readB<PERSON>, set<PERSON><PERSON><PERSON>, createError } from 'h3';
import { loginToTraccar } from '../utils/traccar';
import { loginToTelematics } from '../utils/telematics';
import { loginToMDVR } from '../utils/mdvr';
import { db } from '../database/drizzle';
import { users, traccarUserData, telematicsUserData, mdvrUserData, integrationTokens } from '../database/schema';
import { eq, and } from 'drizzle-orm';
import { add } from 'date-fns';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);

  // Try all login systems
  const [traccarResponse, telematicsResponse, mdvrResponse] = await Promise.allSettled([
    loginToTraccar(body.email, body.password),
    loginToTelematics(body.email, body.password),
    loginToMDVR(body.email, body.password)
  ]);

  // At least one login must succeed
  if (traccarResponse.status === 'rejected' && telematicsResponse.status === 'rejected' && mdvrResponse.status === 'rejected') {
    return createError({
      statusCode: 401,
      statusMessage: 'Invalid credentials for all systems'
    })
  }

  // Get or create user
  let user = await db.query.users.findFirst({
    where: eq(users.email, body.email)
  });

  if (!user) {
    const [newUser] = await db.insert(users)
      .values({
        email: body.email,
        updatedAt: new Date()
      })
      .returning();
    user = newUser;
  }

  // Set user ID cookie
  setCookie(event, 'user_id', user.id, { httpOnly: true, path: '/' });

  // Calculate expiry date (7 days from now)
  const expiry = add(new Date(), { days: 7 });

  // Handle Traccar integration
  if (traccarResponse.status === 'fulfilled') {
    const { sessionId, user: traccarUser } = traccarResponse.value;

    // Update Traccar data
    await db.insert(traccarUserData)
      .values({
        userId: user.id,
        traccarId: traccarUser.id,
        name: traccarUser.name,
        administrator: traccarUser.administrator,
        readonly: traccarUser.readonly,
        deviceLimit: traccarUser.deviceLimit,
        userLimit: traccarUser.userLimit,
        deviceReadonly: traccarUser.deviceReadonly,
        disabled: traccarUser.disabled,
        attributes: traccarUser.attributes,
        updatedAt: new Date()
      })
      .onConflictDoUpdate({
        target: traccarUserData.userId,
        set: {
          traccarId: traccarUser.id,
          name: traccarUser.name,
          administrator: traccarUser.administrator,
          readonly: traccarUser.readonly,
          deviceLimit: traccarUser.deviceLimit,
          userLimit: traccarUser.userLimit,
          deviceReadonly: traccarUser.deviceReadonly,
          disabled: traccarUser.disabled,
          attributes: traccarUser.attributes,
          updatedAt: new Date()
        }
      });

    // Store integration token
    await db.delete(integrationTokens)
      .where(
        and(
          eq(integrationTokens.userId, user.id),
          eq(integrationTokens.provider, 'traccar')
        )
      );

    await db.insert(integrationTokens).values({
      userId: user.id,
      provider: 'traccar',
      token: sessionId,
      pid: traccarUser.id.toString(),
      guid: traccarUser.id.toString(),
      username: traccarUser.name || body.email,
      expiry
    });

    setCookie(event, 'traccar.token', sessionId, { httpOnly: true, path: '/' });
  }

  // Handle Telematics integration
  if (telematicsResponse.status === 'fulfilled') {
    const { status, userApiHash } = telematicsResponse.value;

    // Update Telematics data
    await db.insert(telematicsUserData)
      .values({
        userId: user.id,
        apiHash: userApiHash,
        status: status,
        updatedAt: new Date()
      })
      .onConflictDoUpdate({
        target: telematicsUserData.userId,
        set: {
          apiHash: userApiHash,
          status: status,
          updatedAt: new Date()
        }
      });

    // Store integration token
    await db.delete(integrationTokens)
      .where(
        and(
          eq(integrationTokens.userId, user.id),
          eq(integrationTokens.provider, 'telematics')
        )
      );

    await db.insert(integrationTokens).values({
      userId: user.id,
      provider: 'telematics',
      token: userApiHash,
      pid: status.toString(),
      guid: status.toString(),
      username: body.email,
      expiry
    });

    setCookie(event, 'telematics.token', userApiHash, { httpOnly: true, path: '/' });
  }

  // Handle MDVR integration
  if (mdvrResponse.status === 'fulfilled') {
    const { token, pid, guid, username, permission, roleNumber } = mdvrResponse.value;

    // Update MDVR data
    await db.insert(mdvrUserData)
      .values({
        userId: user.id,
        token: token,
        pid: pid,
        guid: guid,
        username: username,
        permission: permission,
        roleNumber: roleNumber,
        updatedAt: new Date()
      })
      .onConflictDoUpdate({
        target: mdvrUserData.userId,
        set: {
          token: token,
          pid: pid,
          guid: guid,
          username: username,
          permission: permission,
          roleNumber: roleNumber,
          updatedAt: new Date()
        }
      });

    // Store integration token
    await db.delete(integrationTokens)
      .where(
        and(
          eq(integrationTokens.userId, user.id),
          eq(integrationTokens.provider, 'mdvr')
        )
      );

    await db.insert(integrationTokens).values({
      userId: user.id,
      provider: 'mdvr',
      token: token,
      pid: pid,
      guid: guid,
      username: username,
      expiry
    });

    setCookie(event, 'mdvr.token', token, { httpOnly: true, path: '/' });
  }

  return {
    status: 'ok',
    traccarConnected: traccarResponse.status === 'fulfilled',
    telematicsConnected: telematicsResponse.status === 'fulfilled',
    mdvrConnected: mdvrResponse.status === 'fulfilled',
    token: traccarResponse.status === 'fulfilled'
      ? traccarResponse.value.sessionId
      : telematicsResponse.status === 'fulfilled'
        ? telematicsResponse.value.userApiHash
        : mdvrResponse.status === 'fulfilled'
          ? mdvrResponse.value.token
          : null
  };
});
