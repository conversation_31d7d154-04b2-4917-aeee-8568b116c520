import { changeDeviceActive } from '~/server/utils/telematics'

export default defineEventHandler(async (event) => {
	const telematicsSessionId = getCookie(event, 'telematics.token');
	if (!telematicsSessionId) {
		throw createError({
			statusCode: 401,
			message: 'Unauthorized'
		})
	}

	const body = await readBody(event)
	const { deviceId, groupId, active } = JSON.parse(body)

    if (deviceId === undefined && groupId === undefined) {
		throw createError({
			statusCode: 400,
			message: 'Exactly one of deviceId, groupId must be provided'
		})
	}
	try {
		const result = await changeDeviceActive(
			telematicsSessionId,
			deviceId || 0,
			active,
			groupId || 0,
		)
		
		return { success: result }
	} catch (error: any) {
		throw createError({
			statusCode: 500,
			message: error.message || 'Failed to change device active status'
		})
	}
})
