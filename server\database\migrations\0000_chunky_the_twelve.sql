CREATE TABLE "telematics_user_data" (
	"user_id" uuid PRIMARY KEY NOT NULL,
	"api_hash" text NOT NULL,
	"status" integer,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "traccar_user_data" (
	"user_id" uuid PRIMARY KEY NOT NULL,
	"traccar_id" integer NOT NULL,
	"name" text,
	"administrator" boolean DEFAULT false,
	"readonly" boolean DEFAULT false,
	"device_limit" integer DEFAULT -1,
	"user_limit" integer DEFAULT 0,
	"device_readonly" boolean DEFAULT false,
	"disabled" boolean DEFAULT false,
	"attributes" jsonb,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_integrations" (
	"user_id" uuid NOT NULL,
	"provider" text NOT NULL,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "user_integrations_user_id_provider_pk" PRIMARY KEY("user_id","provider")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "telematics_user_data" ADD CONSTRAINT "telematics_user_data_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "traccar_user_data" ADD CONSTRAINT "traccar_user_data_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_integrations" ADD CONSTRAINT "user_integrations_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;