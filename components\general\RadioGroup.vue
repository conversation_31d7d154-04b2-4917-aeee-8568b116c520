<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    required: true
  },
  items: {
    type: Array as () => {text: string, value: string}[],
    default: []
  }
})

const emit = defineEmits(['update:modelValue'])
</script>

<template>
  <div class="space-y-2">
    <div v-for="(item, i) in items" class="space-x-2">
      <general-radio
        :id="`${props.id}${i}`"
        :name="props.id"
        :value="item.value"
        :label="item.text"
        :is-checked="item.value === props.modelValue"
        @update:model-value="emit('update:modelValue', $event)"
      />
    </div>
  </div>
</template>

<style scoped>

</style>