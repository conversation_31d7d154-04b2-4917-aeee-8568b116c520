<script setup lang="ts">
import { useCombobox } from "~/composables/combobox";
import { isEmpty, isSameString } from "~/utils/functions";
import type { ElementEvent } from "~/types/element";

interface Item {
  text: string,
  value: any
}

const props = defineProps({
  modelValue: {
    type: [String, Object as () => any, Array as () => (string | Item)[]],
    default: null
  },
  text: {
    type: String || null,
    default: null
  },
  id: {
    type: String,
    default: ''
  },
  idTrigger: {
    type: [String, Object as () => null],
    default: null
  },
  idTarget: {
    type: [String, Object as () => null],
    default: null
  },
  required: {
    type: Boolean,
    default: false
  },
  plain: {
    type: Boolean,
    default: false
  },
  isInlineAdd: {
    type: Boolean,
    default: false
  },
  closeOnClickAdd: {
    type: Boolean,
    default: false
  },
  isSearchable: {
    type: Boolean,
    default: true
  },
  isMultiSelect: {
    type: Boolean,
    default: false
  },
  isDropdownOnTop: {
    type: Boolean,
    default: false
  },
  addButtonLabel: {
    type: String,
    default: 'Add'
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  searchPlaceholder: {
    type: String,
    default: 'Search'
  },
  activePlaceholder: {
    type: String,
    default: null
  },
  closeOnSelect: {
    type: Boolean,
    default: true
  },
  items: {
    type: Array as () => Item[],
    default: []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  clearSearchOnToggle: {
    type: Boolean,
    default: true
  },
  returnObject: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'mounted',
  'on-show',
  'on-hide',
  'on-change',
  'on-select',
  'on-click-clear',
  'on-input-search',
  'on-click-add',
  'update:modelValue'
])

const searchKey = ref('')
const isHover = ref<boolean>(false)
const isVisible = ref<boolean>(false)
const dropdown = ref(null)

let $combobox: HTMLElement | null = null

const setCombobox: ElementEvent = {
  show: () => toggleCombobox(true),
  hide: () => toggleCombobox(false),
  toggle: () => toggleCombobox(!isVisible.value)
}

onMounted(() => {
  $combobox = document.getElementById(props.idTarget ?? props.id)
  emit('mounted', setCombobox)
})

watch(() => isVisible.value, (isVisible: boolean) => {
  if (props.clearSearchOnToggle) searchKey.value = ''
  isVisible ? (emit('on-show')) : emit('on-hide')
})

const filteredItems = computed(() => {
  return props.items.filter(item => isSameString(item.text, searchKey.value))
})

const selectedItem = computed((): string => {
  if (props.isMultiSelect) return ''
  return props.modelValue ? props.items?.find(item => item.value === props.modelValue)?.text! : props.text
})

const isItemSameAsSearchKey = computed(() => {
  return Boolean(props.items.find(i => i.text.toLowerCase() === searchKey.value.toLowerCase()))
})

function toggleCombobox(value: boolean) {
  if (value) {
    $combobox?.classList.remove('hidden')
    nextTick(() => {
      adjustDropdownPosition();
    });
  } else {
    $combobox?.classList.add('hidden')
  }

  isVisible.value = value;
  useCombobox().value = !useCombobox().value;
}

function adjustDropdownPosition() {
  if (!dropdown.value || !$combobox) return;

  const comboboxRect = $combobox.getBoundingClientRect();
  const dropdownRect = dropdown.value.getBoundingClientRect();
  const spaceBelow = window.innerHeight - comboboxRect.bottom + $combobox.offsetTop;
  const spaceAbove = comboboxRect.top - $combobox.offsetTop;

  if ((spaceBelow < dropdownRect.height && spaceAbove > dropdownRect.height) || props.isDropdownOnTop) {
    dropdown.value.style.top = 'auto';
    dropdown.value.style.bottom = '100%';
  } else {
    dropdown.value.style.top = '100%';
    dropdown.value.style.bottom = 'auto';
  }
}

function onFocusOut() {
  if (!isHover.value) toggleCombobox(false)
}

function onSelectItem(event: Event, item: Item) {
  emit('on-select', props.returnObject ? item : item.value)

  if (props.isMultiSelect) {
    if (props.returnObject) {
      if ((props.modelValue as any[]).find(v => v.value === item.value)) {
        emit('update:modelValue', props.modelValue.filter(v => v.value !== item.value))
      } else {
        emit('update:modelValue', [...props.modelValue, item])
      }
    } else {
      if ((props.modelValue as any[]).includes(item.value)) {
        emit('update:modelValue', (props.modelValue as string[]).filter((value: string) => value !== item.value))
      } else {
        emit('update:modelValue', [...(props.modelValue as string[]), item.value])
      }
    }

  } else {
    emit('update:modelValue', item.value)
  }

  if (!props.closeOnSelect) {
    event.preventDefault()
  } else {
    toggleCombobox(false)
  }
}

function onClickAddButton() {
  toggleCombobox(false)

  if (props.isMultiSelect) {
    searchKey.value = ''
  } else {
    emit('on-click-add')
  }
}

function onClickCreate() {
  if (isItemSameAsSearchKey.value) return
  emit('on-click-add', searchKey.value)
  if (props.closeOnClickAdd) toggleCombobox(false)
}

/**
 * Handles the main input event.
 *
 * @param {string} event - The input event.
 */
function handleMainInput(event: string) {
  if (event.length == 0 && props.isSearchable) {
    if (typeof props.modelValue == 'string') {
      emit('update:modelValue', '')
    } else if (typeof props.modelValue == 'number') {
      emit('update:modelValue', 0)
    }
  }

  searchKey.value = event
}


watch(() => searchKey.value, (searchKey) => {
  emit('on-input-search', searchKey)
})

const placeholder = computed(() => {
  if (!isVisible.value) return props.placeholder

  return props.isSearchable
    ? props.searchPlaceholder ?? 'Type to Search...'
    : props.activePlaceholder
})

function onClickRemoveItem(value: string) {
  if (props.disabled) return
  emit('update:modelValue', (props.modelValue as string[]).filter(v => v !== value))
}

function onClickClear() {
  searchKey.value = ''
  emit('update:modelValue', '')
  emit('on-click-clear')
}
</script>

<template>
  <div class="relative overflow-visible" @focusout="onFocusOut()">
    <div class="relative">
      <general-text-input :clearable="props.clearable" :disabled="props.disabled" :required="props.required"
        :label="props.label" :id="props.idTrigger ?? `${props.id}Activator`"
        :value="isVisible ? searchKey : selectedItem" :placeholder="placeholder" @focus.capture="toggleCombobox(true)"
        @on-input="handleMainInput" @on-click-clear="onClickClear">
        <template #prefix-above>
          <div v-if="props.isMultiSelect" class="pb-2 gap-2 flex flex-wrap">
            <general-chip v-for="value in props.modelValue"
              :label="value.text ?? props.items.find(item => item.value === value)?.text"
              class="!text-sm border border-primary-500">
              <template #suffix>
                <div class="p-0.5 rounded-full transition-all" :class="props.disabled ? 'cursor-not-allowed' : 'cursor-pointer hover:bg-primary-200'"
                  @click="onClickRemoveItem(value)">
                  <icon-close size="16" class="stroke-primary-500" />
                </div>
              </template>
            </general-chip>
          </div>
        </template>
        <template #suffix>
          <div>
            <slot name="suffix" />
          </div>

          <icon-chevron-up v-if="!$slots.suffix" size="20" class="cursor-pointer transition-transform"
            :class="isVisible ? 'rotate-0 stroke-primary-500' : 'rotate-180 stroke-gray-500'"
            @click="toggleCombobox(!isVisible)" />
        </template>
      </general-text-input>
    </div>

    <div ref="dropdown" :id="props.idTarget ?? props.id"
      class="absolute left-0 right-0 top-[100%] z-40 transition hidden" @mouseover="isHover = true"
      @mouseleave="isHover = false">
      <div class="bg-white mx-1 rounded-lg border shadow-elevated overflow-hidden">
        <div v-if="!$props.isInlineAdd && !props.plain" class="shadow-sm">
          <button type="button"
            class="flex items-center justify-start w-full py-2.5 px-3.5 text-sm font-[600] text-primary-500 transition hover:bg-primary-50"
            @mousedown.prevent="onClickAddButton()">
            <icon-plus size="20" class="mr-3 stroke-primary-500" />
            <span>{{ props.addButtonLabel }}</span>
          </button>
        </div>

        <div v-if="props.isLoading" class="py-2.5 flex justify-center">
          <svg aria-hidden="true" class="w-6 h-6 animate-spin text-primary-300 fill-white" viewBox="0 0 100 101"
            fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
              fill="currentColor" />
            <path
              d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
              fill="currentFill" />
          </svg>
        </div>

        <div v-else>
          <div v-if="!isEmpty(searchKey)" class="shadow-sm">
            <button v-if="props.isInlineAdd" :disabled="isItemSameAsSearchKey" type="button"
              class="py-2.5 px-3.5 w-full text-left hover:bg-gray-50"
              :class="isItemSameAsSearchKey ? 'bg-gray-50 cursor-not-allowed' : ''" @mousedown.prevent="onClickCreate">
              <p class="text-sm font-semibold space-x-3">
                <span class="text-gray-900">Create</span>
                <span class="font-normal py-1 px-3 bg-primary-100 text-primary-500 rounded">{{ searchKey }}</span>
              </p>
            </button>
          </div>

          <ul v-if="(props.isSearchable ? filteredItems : props.items).length > 0" class="max-h-52 overflow-y-auto">
            <li v-for="(item, i) in (props.isSearchable ? filteredItems : props.items)"
              :id="`${props.idTarget ?? props.id}${i}`"
              :class="[`${Array.isArray(props.modelValue) ? (modelValue as string[]).includes(item.value) : item.value === modelValue ? 'bg-gray-50' : ''}`, 'py-2.5 px-3.5 flex items-center justify-between cursor-pointer transition hover:bg-gray-100']"
              @mousedown="onSelectItem($event, item)">
              <p>{{ item.text }}</p>
              <icon-check
                v-if="Array.isArray(props.modelValue) ? (props.returnObject ? modelValue.find(v => v.value === item.value) : (modelValue as string[]).includes(item.value)) : item.value === modelValue"
                size="20" class="stroke-primary-500" />
            </li>
          </ul>

          <p v-if="(props.isSearchable ? filteredItems : props.items).length === 0" class="py-3.5 text-center cursor-default text-gray-500" @mousedown.prevent="">
            No Items
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
