<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Object as () => { hours: number, minutes: number },
    default: () => ({ hours: (new Date()).getHours(), minutes: (new Date()).getMinutes() })
  },
  minTime: {
    type: Object as () => { hours: number, minutes: number },
    default: () => ({ hours: 0, minutes: 0 })
  },
  maxTime: {
    type: Object as () => { hours: number, minutes: number },
    default: () => ({ hours: 23, minutes: 59 })
  }
})

const isFocused = ref([false, false])
const emit = defineEmits(['update:model-value'])

function onInput(value: number, isHour: boolean) {
  const hours = isHour
    ? constrain(Number(value), props.minTime.hours, props.maxTime.hours)
    : Number(props.modelValue.hours)

  const minutes = isHour
    ? Number(props.modelValue.minutes)
    : constrain(Number(value), props.minTime.minutes, props.maxTime.minutes)

  emit('update:model-value', { hours, minutes })
}

function changeValue(step: number, isHour: boolean) {
  let newValue = (isHour ? props.modelValue.hours : props.modelValue.minutes) + step

  if (newValue > (isHour ? props.maxTime.hours : props.maxTime.minutes)) newValue = isHour ? props.minTime.hours : props.minTime.minutes
  if (newValue < (isHour ? props.minTime.hours : props.minTime.minutes)) newValue = isHour ? props.maxTime.hours : props.maxTime.minutes

  onInput(newValue, isHour)
}

function onScroll(event: WheelEvent, isHour: boolean) {
  event.deltaY < 0
    ? changeValue(1, isHour)
    : changeValue(-1, isHour)
}

function onKeydown(event: KeyboardEvent, isHour: boolean) {
  switch (event.key) {
    case 'ArrowUp': changeValue(1, isHour); break
    case 'ArrowDown': changeValue(-1, isHour); break
  }
}
</script>

<template>
  <div class="flex items-center space-x-2">
    <div
      class="flex border rounded-lg overflow-hidden"
      :class="isFocused[0] ? 'border-primary-500' : 'border-gray-500'"
    >
      <input
        type="number"
        :value="props.modelValue.hours < 10 ? `0${props.modelValue.hours}` : props.modelValue.hours"
        :min="props.minTime?.hours" :max="props.maxTime?.hours"
        @wheel.passive="onScroll($event, true)"
        @input="onInput($event.target?.value, true)"
        @keydown="onKeydown($event, true)"
        @focus="isFocused[0] = true"
        @blur="isFocused[0] = false"
        class="w-10 p-1 text-sm border text-center hide-spin-button border-none focus:ring-0"
      >

      <div class="flex flex-col p-1 bg-gray-50">
        <icon-chevron-up
          size="16"
          class="cursor-pointer stroke-gray-500 hover:stroke-gray-900"
          @click="changeValue(1, true)"
        />
        <icon-chevron-down
          size="16"
          class="cursor-pointer stroke-gray-500 hover:stroke-gray-900"
          @click="changeValue(-1, true)"
        />
      </div>
    </div>

    <p>:</p>

    <div
      class="flex border rounded-lg overflow-hidden"
      :class="isFocused[1] ? 'border-primary-500' : 'border-gray-500'"
    >
      <input
        type="number"
        :value="props.modelValue.minutes < 10 ? `0${props.modelValue.minutes}` : props.modelValue.minutes"
        :min="props.minTime?.minutes"
        :max="props.maxTime?.minutes"
        @wheel.passive="onScroll($event, false)"
        @input="onInput($event.target?.value, false)"
        @keydown="onKeydown($event, false)"
        @focus="isFocused[1] = true"
        @blur="isFocused[1] = false"
        class="w-10 p-1 text-sm border text-center hide-spin-button border-none focus:ring-0"
      >

      <div class="flex flex-col p-1 bg-gray-50">
        <icon-chevron-up
          size="16"
          class="cursor-pointer stroke-gray-500 hover:stroke-gray-900"
          @click="changeValue(1, false)"
        />
        <icon-chevron-down
          size="16"
          class="cursor-pointer stroke-gray-500 hover:stroke-gray-900"
          @click="changeValue(-1, false)"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
select {
  background: none;
  padding: 0;
}

.hide-spin-button {
  -moz-appearance: textfield;
}

.hide-spin-button::-webkit-outer-spin-button,
.hide-spin-button::-webkit-inner-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
</style>