<script setup lang="ts">
import { Line as LineChart } from 'vue-chartjs'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend
} from 'chart.js'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend
)

const props = defineProps({
  labels: {
    type: Array as () => string[],
    default: []
  },
  datasets: {
    type: Array as () => {label: string, color: string, data: any[]}[],
    default: []
  }
})

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      ticks: {
        font: {size: 14},
        color: '#667085'
      }
    },
    y: {
      min: 0,
      ticks: {
        stepSize: 1,
        font: {size: 14},
        color: '#667085'
      }
    }
  },
  plugins: {
    legend: {
      align: 'end',
      labels: {
        font: {size: 14},
        color: '#667085',
        boxHeight: 8,
        usePointStyle: true
      }
    }
  }
})

const chartData = computed(() => {
  return {
    labels: props.labels,
    datasets: props.datasets.map(dataset => ({
      label: dataset.label,
      borderColor: dataset.color,
      backgroundColor: dataset.color,
      pointBanckgroundColor: dataset.color,
      data: [...dataset.data]
    }))
  }
})
</script>

<template>
  <line-chart
    :data="chartData"
    :options="chartOptions"
    style="height: 100%;"
  />
</template>