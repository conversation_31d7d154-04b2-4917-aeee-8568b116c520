<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 43 42" fill="none">
    <path
      d="M16.25 21L19.75 24.5L27.625 16.625M31.8271 8.74739C32.1874 9.61893 32.8791 10.3117 33.7501 10.6734L36.8042 11.9385C37.6757 12.2995 38.3682 12.992 38.7292 13.8636C39.0903 14.7352 39.0903 15.7146 38.7292 16.5862L37.4651 19.6382C37.1039 20.5102 37.1034 21.4905 37.4662 22.3621L38.7282 25.4131C38.9072 25.8448 38.9993 26.3076 38.9994 26.7749C38.9995 27.2422 38.9075 27.705 38.7286 28.1368C38.5498 28.5685 38.2876 28.9608 37.9571 29.2912C37.6266 29.6216 37.2343 29.8836 36.8025 30.0623L33.7506 31.3265C32.8791 31.6869 32.1864 32.3786 31.8247 33.2496L30.5597 36.3038C30.1986 37.1754 29.5062 37.8679 28.6346 38.2289C27.763 38.59 26.7837 38.59 25.9121 38.2289L22.8602 36.9648C21.9886 36.6046 21.0097 36.6053 20.1386 36.9668L17.0846 38.2301C16.2135 38.5903 15.235 38.59 14.3641 38.2292C13.4933 37.8685 12.8012 37.1768 12.4399 36.3061L11.1745 33.251C10.8142 32.3795 10.1225 31.6867 9.25148 31.325L6.19741 30.06C5.32621 29.6991 4.63395 29.007 4.27279 28.1359C3.91164 27.2648 3.91115 26.2859 4.27143 25.4145L5.53556 22.3625C5.89569 21.4908 5.89496 20.5118 5.53352 19.6407L4.2712 16.5844C4.09223 16.1527 4.00007 15.6899 4 15.2226C3.99993 14.7553 4.09193 14.2925 4.27076 13.8608C4.4496 13.429 4.71175 13.0367 5.04224 12.7063C5.37274 12.3759 5.76509 12.1139 6.19689 11.9352L9.24877 10.671C10.1195 10.3109 10.8118 9.6201 11.1738 8.7501L12.4388 5.69592C12.7998 4.8243 13.4923 4.13181 14.3639 3.77078C15.2355 3.40974 16.2148 3.40974 17.0864 3.77078L20.1382 5.03495C21.0098 5.3951 21.9888 5.39436 22.8598 5.03291L25.9152 3.77274C26.7867 3.4119 27.7658 3.41198 28.6372 3.77294C29.5086 4.1339 30.2009 4.8262 30.562 5.69759L31.8274 8.75268L31.8271 8.74739Z"
      stroke="#039855" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>