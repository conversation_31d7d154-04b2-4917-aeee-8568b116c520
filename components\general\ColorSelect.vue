<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  label: {
    type: String,
    default: 'Main Color'
  },
  items: {
    type: Array as () => string[],
    default: () => []
  }
})

const emit = defineEmits(['update:model-value'])
</script>

<template>
  <div class="space-y-2">
    <p class="text-sm font-medium text-gray-700">{{ props.label }}</p>

    <div class="flex space-x-3">
      <div v-for="item in items"
        :class="`w-10 h-10 rounded-full outline outline-2 outline-offset-4 bg-${item}-500 cursor-pointer ${props.modelValue === item ? `outline-${item}-500 ` : 'outline-none'}`"
        @click="emit('update:model-value', item)" />
    </div>
  </div>
</template>

<style scoped></style>