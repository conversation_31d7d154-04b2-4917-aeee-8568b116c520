import { defineE<PERSON><PERSON><PERSON><PERSON>, readBody, createError } from 'h3'
import { db } from '~/server/database/drizzle'
import { integrationTokens } from '~/server/database/schema'
import { eq, and } from 'drizzle-orm'
import { requireUserSession } from '~/server/utils/session'

export default defineEventHandler(async (event) => {
	try {
		const userId = getCookie(event, 'user_id')

		if (!userId) {
			throw createError({
				statusCode: 401,
				statusMessage: 'Unauthorized'
			})
		}

		const body = await readBody(event)

		// Get the latest valid MDVR token
		const token = await db.query.integrationTokens.findFirst({
			where: and(
				eq(integrationTokens.userId, userId),
				eq(integrationTokens.provider, 'mdvr')
			),
			orderBy: (tokens) => [tokens.createdAt]
		})

		if (!token?.token) {
			throw createError({
				statusCode: 401,
				statusMessage: 'Unauthorized: No valid MDVR token found'
			})
		}

		const response = await fetch('https://mdvr.transtrack.id/vss/record/findEvidences.action', {
			method: 'POST',
			headers: {
				'accept': 'application/json, text/plain, */*',
				'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
				'platform': 'web',
				'version': 'v2'
			},
			body: new URLSearchParams({
				alarmType: body.alarmType || '',
				startTime: body.startTime,
				endTime: body.endTime,
				pageNum: body.pageNum?.toString() || '',
				pageCount: body.pageCount?.toString() || '30',
				token: token.token,
				scheme: 'https',
				lang: 'en'
			})
		})

		const data = await response.json()

		// Check for MDVR authentication errors
		if (data.status === 10001 || data.status === 10002 || data.msg?.includes('token')) {
			throw createError({
				statusCode: 401,
				statusMessage: 'MDVR session expired or invalid'
			})
		}

		// Check if the response is successful and contains data
		if (data.status === 10000 && data.data?.list) {
			const evidences = data.data.list.map((item: any) => {
				const imageFiles = item.alarmFile?.filter((file: any) => file.fileType === "4")
				const videoFiles = item.alarmFile?.filter((file: any) => file.fileType === "2")

				return {
					...item,
					imageUrl: imageFiles?.length > 0 ? imageFiles[0].downUrl : null,
					videoUrl: videoFiles?.length > 0 ? videoFiles[0].downUrl : null,
					location: item.alarmGps ? item.alarmGps.split(',').join(', ') : null
				}
			})

			return {
				status: 10000, // Keep the original status code
				msg: "Success",
				data: {
					total: data.data.total,
					list: evidences
				}
			}
		} else {
			// If the response is not successful or contains no data
			return {
				status: data.status || 500,
				msg: data.msg || "No data available",
				data: {
					total: 0,
					list: []
				}
			}
		}

	} catch (error: any) {
		throw createError({
			statusCode: error.statusCode || 500,
			message: error.message || 'Failed to fetch evidences'
		})
	}
})