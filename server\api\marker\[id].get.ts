import { defineEvent<PERSON><PERSON><PERSON>, setHeader, getQuery } from 'h3'

function getStatusColor(status: string) {
	switch (status.toLowerCase()) {
		case 'online': // GREEN
			return [
				"#19BB07",// 1
				"#1CC166",// 2
				"#3BCF2A",// 3
				"#0E9F6E",// 4
				"#4EB531",// 5
				"#454545",// 6
				"#8DEC18",// 7
				"#0D0503",// 8
				"#34C62F",// 9
			];
		case 'offline': // RED
			return [
				"#BB0707",
				"#C11C1C",
				"#CF2A2A",
				"#9F0E0E",
				"#B53131",
				"#454545",
				"#EC1818",
				"#030503",
				"#C62F2F",
			];
		case 'waiting': // YELLOW
			return [
				"#BBB807",
				"#D9C215",
				"#C4CF2A",
				"#FACA15",
				"#B5AF31",
				"#454545",
				"#FFDF10",
				"#0D0503",
				"#BDC54C",
			];
		default: // GRAY
			return [
				"#070707",
				"#1C1C1C",
				"#2A2A2A",
				"#0E0E0E",
				"#313131",
				"#454545",
				"#181818",
				"#030303",
				"#2F2F2F",
			];
	}
}

export default defineEventHandler(async (event) => {
	setHeader(event, 'Content-Type', 'image/svg+xml')
	const query = getQuery(event)
	const name = query.name as string || 'Unknown'
	const status = query.status as string || 'offline'

	const [color1, color2, color3, color4, color5, color6, color7, color8, color9] = getStatusColor(status)

	return `
		<svg width="97" height="101" viewBox="0 0 97 101" fill="none" xmlns="http://www.w3.org/2000/svg" >
		<g transform="rotate(180 50.5 35.5)">
<path d="M56.5896 15.2766L56.6397 13.8401C56.7037 12.0082 55.2704 10.4716 53.4389 10.4077L45.0825 10.1158C43.251 10.0519 41.714 11.4848 41.6501 13.3166L41.5865 15.1367C43.1393 13.9933 46.0808 13.2858 49.4293 13.4028C52.405 13.5063 55.0149 14.2384 56.5896 15.2766Z" fill="${color1}"/>
<path d="M55.9395 33.8796L56.5891 15.2764C55.0144 14.2382 52.4045 13.5065 49.4285 13.4025C46.0804 13.2856 43.1388 13.9931 41.5857 15.1365L40.9494 33.3562L55.9395 33.8796Z" fill="${color1}"/>
<path d="M56.5747 15.7037L56.6129 14.6105C54.8441 13.7328 52.3128 13.1414 49.4417 13.0411C46.2606 12.9301 43.4534 13.4483 41.6133 14.3604L41.5734 15.5027C43.0898 14.5834 45.9096 13.9135 49.4069 14.0357C52.5075 14.1439 55.0325 14.8359 56.5747 15.7037Z" fill="${color2}"/>
<path d="M56.5953 15.1109L56.6335 14.0169C54.8646 13.1396 52.3334 12.5486 49.4622 12.4483C46.2812 12.3373 43.4739 12.8555 41.6339 13.7676L41.5939 14.9106C43.1104 13.9899 45.9301 13.3215 49.4274 13.4436C52.528 13.5523 55.0531 14.2435 56.5953 15.1109Z" fill="${color3}"/>
<path d="M41.637 32.7162L42.3136 13.3398C42.3647 11.8771 43.5967 10.7282 45.0595 10.7793L53.4159 11.0711C54.879 11.1222 56.0275 12.3542 55.9765 13.817L55.2998 33.1933L41.637 32.7162ZM40.9504 33.3564L55.9404 33.8799L56.6402 13.8401C56.7042 12.0083 55.2709 10.4717 53.4394 10.4077L45.083 10.1159C43.2515 10.0519 41.7145 11.4848 41.6506 13.3167L40.9508 33.3564L40.9504 33.3564Z" fill="${color4}"/>
<path d="M46.3227 53.0302C47.0738 53.6564 48.4146 53.7032 49.2083 53.131C51.2798 51.6358 54.8133 48.5564 54.9422 44.8646C55.8542 36.1348 55.8517 36.1301 55.9611 33.2593C56.089 29.9218 55.0168 17.5238 55.0168 17.5238C55.0621 16.2266 53.7158 15.1272 52.0112 15.0677L46.1807 14.864C44.4749 14.8045 43.0556 15.8073 43.0103 17.1045C42.9651 18.4009 41.0758 29.3975 40.9707 32.7358C40.8793 35.6465 41.1787 44.384 41.1787 44.384C41.0505 48.0754 44.3595 51.3937 46.3227 53.0302Z" fill="${color4}"/>
<path d="M51.9776 16.0623C53.0917 16.1012 54.0478 16.768 54.0227 17.4887L54.0206 17.5479L54.0265 17.6082C54.0374 17.7318 55.0915 29.9836 54.9675 33.2204C54.8583 36.0651 53.9611 44.6745 53.9523 44.7609L53.9485 44.7954L53.9473 44.8295C53.8334 48.0911 50.5644 50.9246 48.6256 52.3229C48.4263 52.4665 48.1125 52.5457 47.7852 52.5342C47.4578 52.5228 47.1492 52.4219 46.9608 52.2648C45.1243 50.7342 42.0612 47.68 42.1751 44.4184L42.1763 44.3843L42.1752 44.3496C42.1722 44.2625 41.8774 35.6114 41.968 32.7665C42.0309 30.7574 42.7896 25.6889 43.344 21.9877C43.755 19.2418 43.9888 17.663 44.0071 17.1389C44.0323 16.4182 45.0322 15.8197 46.1475 15.8587L51.9776 16.0623Z" fill="${color2}"/>
<path d="M44.7535 38.7228C44.1355 38.697 43.5473 38.5548 43.0025 38.3274C43.0684 41.3287 43.1684 44.267 43.17 44.3191L43.1719 44.3861L43.1695 44.4533C43.0667 47.397 46.2325 50.3621 47.5824 51.4886C47.6066 51.5035 47.6975 51.5352 47.8198 51.5395C47.9413 51.5437 48.0338 51.5181 48.0434 51.5157C49.4845 50.4757 52.8495 47.7383 52.9522 44.7949L52.9546 44.7277L52.962 44.6561C52.9671 44.6084 53.2562 41.8297 53.5261 38.9215C50.6097 39.1037 47.7053 38.8452 44.7535 38.7228Z" fill="${color2}"/>
<path d="M42.8308 38.0759L43.0665 38.1743C43.6181 38.4045 44.1877 38.5332 44.7602 38.5574C45.4652 38.5865 46.1681 38.6236 46.8686 38.6606C47.7453 38.7068 48.6185 38.7529 49.4907 38.7834C51.0632 38.8383 52.3417 38.8297 53.5157 38.7563L53.7093 38.744L53.6911 38.9373C53.418 41.8815 53.1298 44.6476 53.1269 44.6738L53.1195 44.7454L53.1179 44.8016C53.0126 47.8174 49.6018 50.5962 48.1403 51.6512L48.0851 51.677C48.0459 51.6874 47.9428 51.7104 47.8137 51.7059C47.6713 51.7009 47.5517 51.6648 47.4962 51.631C46.096 50.465 42.8981 47.462 43.0033 44.4484L43.0019 44.2704C42.9704 43.3226 42.8915 40.8467 42.8363 38.3316L42.8308 38.0759ZM49.4792 39.1145C48.605 39.084 47.7295 39.0378 46.851 38.9916C46.1516 38.9546 45.4506 38.9176 44.7464 38.8884C44.2156 38.8664 43.6878 38.7602 43.1739 38.5727C43.2287 40.9987 43.3035 43.3426 43.3339 44.2584L43.3352 44.4592C43.2351 47.3266 46.3464 50.2411 47.6885 51.3613C47.6748 51.3498 47.7349 51.3709 47.8253 51.374C47.888 51.3762 47.9411 51.3686 47.9729 51.3621C49.4095 50.3215 52.6869 47.6407 52.7864 44.7896L52.797 44.6394C52.7998 44.6137 53.075 41.9684 53.3429 39.0989C52.2108 39.162 50.9765 39.1668 49.4792 39.1145Z" fill="${color5}"/>
<path d="M54.4378 39.3656L48.1861 41.0346L48.3344 36.7884L54.657 36.1713L54.4378 39.3656Z" fill="${color6}"/>
<path d="M54.1364 39.2788L48.472 40.7972L48.6086 36.8863L54.4337 36.3822L54.1364 39.2788Z" fill="${color7}"/>
<path d="M42.0665 38.9336L48.1865 41.0347L48.3348 36.7885L42.0711 35.7318L42.0665 38.9336Z" fill="${color6}"/>
<path d="M42.372 38.868L47.917 40.7778L48.0536 36.8669L42.2774 35.9577L42.372 38.868Z" fill="${color7}"/>
<path d="M46.2991 45.1019L47.6172 43.8735C47.8845 43.6231 48.3049 43.6378 48.5552 43.9062L49.7836 45.2235C50.0332 45.4915 50.0185 45.9119 49.7508 46.1619L48.4335 47.3903C48.1651 47.6402 47.7455 47.6256 47.4956 47.3575L46.2664 46.0398C46.0164 45.7722 46.0311 45.3522 46.2991 45.1019Z" fill="${color7}"/>
<path d="M54.5023 35.883L54.8328 35.9018C54.7953 36.9756 48.5996 36.7714 48.3356 36.7622C48.0717 36.753 41.8772 36.5245 41.9147 35.4506L42.2462 35.4622C42.3398 35.732 44.4591 36.2949 48.3472 36.4307C52.2349 36.5665 54.3888 36.1528 54.5023 35.883Z" fill="${color8}"/>
<path d="M41.6519 33.2309C41.8395 33.2375 41.9864 33.3947 41.9799 33.5827C41.9638 34.044 44.2457 35.0233 48.4858 35.1713C52.726 35.3194 55.0707 34.5021 55.0868 34.0404C55.0933 33.8528 55.2506 33.7058 55.4385 33.7124C55.6261 33.7189 55.7731 33.8761 55.7665 34.0641C55.7176 35.4649 51.9462 35.9723 48.4625 35.8507C44.9788 35.729 41.252 34.9602 41.3009 33.5589C41.3067 33.3709 41.4643 33.2244 41.6519 33.2309Z" fill="${color7}"/>
<path d="M49.6232 24.7424L52.4951 24.8427L52.6777 30.4783L49.4268 30.3647L49.6232 24.7424Z" fill="${color7}"/>
<path d="M45.0172 24.5816L47.8887 24.6818L47.6924 30.3042L44.4419 30.1907L45.0172 24.5816Z" fill="${color7}"/>
<path d="M42.7748 21.7273L54.9316 22.1519L54.9788 20.8011L42.822 20.3766L42.7748 21.7273Z" fill="${color4}"/>
<path d="M48.4868 32.134C51.38 32.235 53.7366 32.0013 53.7502 31.6121L53.5415 23.9146L44.0181 23.582L43.2725 31.2462C43.259 31.6354 45.5932 32.0329 48.4868 32.134Z" fill="${color5}"/>
<path d="M55.3686 38.2218C55.4499 38.2246 55.5188 38.2875 55.5277 38.3707C55.5393 38.4836 56.6167 49.7223 50.3322 53.7557C50.0605 53.928 49.8307 53.8876 49.6866 53.8233C48.9142 53.48 48.5693 51.5673 48.532 51.3501C48.5165 51.2598 48.5773 51.1741 48.6679 51.1586C48.7586 51.1428 48.8439 51.2043 48.8594 51.2942C48.9871 52.0461 49.3618 53.3154 49.8215 53.5196C49.8874 53.5489 49.994 53.577 50.1534 53.4757C56.2679 49.5516 55.209 38.5151 55.1977 38.404C55.1883 38.3128 55.2546 38.2315 55.3458 38.2221C55.3534 38.222 55.3607 38.2215 55.3686 38.2218Z" fill="${color9}"/>
<path d="M54.0725 43.0854C54.1082 43.0866 54.1435 43.0992 54.1726 43.1235L55.3036 44.0648C55.3737 44.1235 55.3838 44.228 55.3247 44.2986C55.266 44.3688 55.1615 44.378 55.0912 44.3201L53.9603 43.3788C53.8901 43.32 53.8801 43.2155 53.9392 43.145C53.9733 43.1039 54.0231 43.0836 54.0725 43.0854Z" fill="${color9}"/>
<path d="M53.4118 46.5448C53.4411 46.5458 53.4704 46.5548 53.4968 46.5717L54.5927 47.2848C54.6693 47.3346 54.6912 47.4376 54.641 47.5142C54.5908 47.5915 54.4874 47.6126 54.4112 47.5628L53.3153 46.8497C53.2387 46.7999 53.2168 46.6969 53.267 46.6203C53.2999 46.5697 53.356 46.5428 53.4118 46.5448Z" fill="${color9}"/>
<path d="M51.9914 49.0916C52.0336 49.0931 52.0752 49.1101 52.1063 49.1435L53.2437 50.3478C53.3067 50.4142 53.3038 50.5194 53.237 50.5821C53.1705 50.646 53.0653 50.6423 53.0027 50.5755L51.8653 49.3712C51.8023 49.3044 51.8052 49.1995 51.872 49.1369C51.905 49.1049 51.9489 49.0901 51.9914 49.0916Z" fill="${color9}"/>
<path d="M50.611 50.5402C50.6725 50.5424 50.7306 50.5794 50.757 50.6396L51.6129 52.5963C51.6499 52.6805 51.6115 52.7784 51.5273 52.815C51.4439 52.8513 51.3456 52.8136 51.309 52.7295L50.4531 50.7727C50.4165 50.6886 50.4545 50.5907 50.5387 50.5541C50.5622 50.5439 50.5871 50.5394 50.611 50.5402Z" fill="${color9}"/>
<path d="M41.2458 37.7291C41.2538 37.7294 41.261 37.73 41.2689 37.7314C41.3592 37.7471 41.4197 37.8329 41.404 37.9232C41.385 38.0332 39.5598 48.9705 45.3842 53.3104C45.5347 53.4217 45.6434 53.4016 45.7123 53.377C46.2453 53.1831 46.6981 51.7572 46.8277 51.2241C46.8494 51.1355 46.9372 51.0808 47.0281 51.1022C47.1171 51.1239 47.1721 51.2137 47.1504 51.3027C47.0984 51.5164 46.6205 53.4 45.8257 53.6889C45.6763 53.7434 45.4451 53.7676 45.1863 53.5772C39.1978 49.1145 41.0576 37.9784 41.0771 37.8665C41.0914 37.7838 41.1649 37.7263 41.2458 37.7291Z" fill="${color9}"/>
<path d="M42.1995 42.6707C42.2488 42.6724 42.2971 42.6961 42.3282 42.7394C42.3819 42.8135 42.365 42.9175 42.2909 42.9712L41.0974 43.8313C41.0241 43.885 40.9197 43.8681 40.8661 43.7936C40.8124 43.7195 40.8293 43.6155 40.9034 43.5618L42.0969 42.7017C42.1277 42.6796 42.1642 42.6694 42.1995 42.6707Z" fill="${color9}"/>
<path d="M42.6172 46.1678C42.673 46.1698 42.7271 46.2006 42.7564 46.2533C42.8012 46.3336 42.7722 46.4344 42.6923 46.4788L41.5493 47.1137C41.4698 47.1581 41.3681 47.1298 41.3234 47.0492C41.2787 46.9689 41.3077 46.868 41.3876 46.8237L42.5306 46.1888C42.5581 46.1737 42.588 46.1668 42.6172 46.1678Z" fill="${color9}"/>
<path d="M43.8569 48.8077C43.8994 48.8092 43.9421 48.827 43.9732 48.8612C44.0355 48.9284 44.0311 49.0336 43.9635 49.0954L42.745 50.2174C42.6778 50.279 42.5726 50.2757 42.5107 50.2077C42.4484 50.1406 42.4529 50.0354 42.5204 49.9735L43.739 48.8515C43.7724 48.8207 43.8147 48.8062 43.8569 48.8077Z" fill="${color9}"/>
<path d="M45.1318 50.3491C45.1557 50.3499 45.1802 50.3561 45.2026 50.3679C45.284 50.4103 45.3155 50.5106 45.2727 50.592L44.2828 52.4843C44.24 52.5657 44.1397 52.5964 44.0591 52.5544C43.9777 52.5116 43.9466 52.4117 43.9889 52.3303L44.9789 50.438C45.0098 50.3798 45.0703 50.3469 45.1318 50.3491Z" fill="${color9}"/>
</g>
<rect x="10" y="64.0645" width="77" height="26" rx="4" fill="${color4}"/>
<text x="21" y="81.0645" font-family="Inter" font-size="12" font-weight="700" fill="#FFFFFF">` + name + `</text>
</svg>

	`.trim()
})


