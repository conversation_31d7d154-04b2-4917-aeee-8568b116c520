<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="size" :height="size" viewBox="0 0 21 20" fill="none">
    <path d="M8.24984 3.33366V1.66699M13.2498 16.667V18.3337M4.08317 7.50033H2.4165M17.4165 12.5003H19.0832M10.7498 14.7144L8.98207 16.4821C7.68032 17.7839 5.56977 17.7839 4.26802 16.4821C2.96628 15.1804 2.96628 13.0698 4.26802 11.7681L6.03579 10.0003M15.4639 10.0003L17.2317 8.23256C18.5334 6.93081 18.5334 4.82026 17.2317 3.51851C15.9299 2.21677 13.8194 2.21677 12.5176 3.51851L10.7498 5.28628" stroke="#101828" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>