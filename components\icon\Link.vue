<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 21 20" fill="none">
    <path d="M11.3396 15.3034L10.161 16.4819C8.53386 18.1091 5.89567 18.1091 4.26848 16.4819C2.6413 14.8547 2.6413 12.2165 4.26848 10.5893L5.447 9.4108M16.0536 10.5893L17.2321 9.4108C18.8593 7.78361 18.8593 5.14542 17.2321 3.51824C15.6049 1.89106 12.9667 1.89106 11.3396 3.51824L10.161 4.69675M7.83363 12.9167L13.667 7.08337" stroke="#101828" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>