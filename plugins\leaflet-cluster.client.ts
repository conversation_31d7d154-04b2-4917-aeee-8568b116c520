import 'leaflet.markercluster/dist/MarkerCluster.css'
import 'leaflet.markercluster/dist/MarkerCluster.Default.css'
import * as L from 'leaflet'
import 'leaflet.markercluster'

export default defineNuxtPlugin(() => {
	// Only execute on client-side
	if (process.client) {
		// Define default cluster options
		const defaultClusterOptions: L.MarkerClusterGroupOptions = {
			maxClusterRadius: 80, // Increase cluster radius
			spiderfyOnMaxZoom: false, // Disable spiderfy for better performance
			showCoverageOnHover: false, // Disable coverage display
			zoomToBoundsOnClick: true,
			animate: false, // Disable animations
			chunkedLoading: true, // Enable chunked loading
			chunkInterval: 50, // Process markers in chunks
			chunkDelay: 10, // Add delay between chunks
			removeOutsideVisibleBounds: true // Remove markers outside view
		}

		// Provide the MarkerClusterGroup factory function
		return {
			provide: {
				createMarkerCluster: (options?: L.MarkerClusterGroupOptions) => {
					return new L.MarkerClusterGroup({
						...defaultClusterOptions,
						...options
					})
				}
			}
		}
	}
})
