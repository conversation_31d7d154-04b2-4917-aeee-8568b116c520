<script setup lang="ts">
import {constrain} from "~/utils/functions";

const props = defineProps({
  id: {
    type: String,
    default: ''
  },
  modelValue: {
    type: Number,
    default: 0
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 99
  }
})

const emit = defineEmits(['update:modelValue'])

function onClickIncrease() {
  emit('update:modelValue', constrain(props.modelValue + 1, props.min, props.max))
}

function onClickDecrease() {
  emit('update:modelValue', constrain(props.modelValue - 1, props.min, props.max))
}

function onFocusIn($element: HTMLInputElement) {
  $element.value = ''
}

function onFocusOut($element: HTMLInputElement) {
  $element.value = props.modelValue < 10 ? `0${props.modelValue}` : String(props.modelValue)
}

function onInput($element: HTMLInputElement) {
  const value = constrain(Number($element.value), props.min, props.max)
  $element.value = String(value)
  emit('update:modelValue', value)
}
</script>

<template>
  <div class="flex flex-col items-center space-y-1">
    <general-icon-button
      :disabled="props.modelValue >= props.max"
      class="w-6 h-6 !rounded-full border-none"
      @on-click="onClickIncrease"
    >
      <template #icon>
        <icon-chevron-up size="20"/>
      </template>
    </general-icon-button>
    <input
      :value="props.modelValue < 10 ? `0${props.modelValue}` : props.modelValue"
      :id="props.id"
      type="number"
      class="text-center w-full p-1 text-2xl border-none rounded-lg transition focus:bg-gray-100 focus:border-none focus:ring-0"
      :min="props.min"
      :max="props.max"
      @focusin="onFocusIn($event.target)"
      @focusout="onFocusOut($event.target)"
      @input="onInput($event.target)"
    >
    <general-icon-button
      :disabled="props.modelValue <= props.min"
      class="w-6 h-6 !rounded-full border-none"
      @on-click="onClickDecrease"
    >
      <template #icon>
        <icon-chevron-up size="20" class="rotate-180"/>
      </template>
    </general-icon-button>
  </div>
</template>

<style scoped>
input[type='number'] {
  -moz-appearance:textfield;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
</style>
