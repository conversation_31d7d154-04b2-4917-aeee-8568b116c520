export const useMdvrConnection = () => {
  const { data } = useAuth()
  const connectionStatus = ref<'connected' | 'disconnected' | 'checking'>('checking')
  const lastChecked = ref<Date | null>(null)
  
  // Check if user has MDVR token
  const hasMdvrToken = computed(() => {
    return data.value && Boolean(data.value.integrationTokens.find(i => i.provider === 'mdvr'))
  })

  // Check MDVR connection status by calling the token endpoint
  const checkConnectionStatus = async (): Promise<boolean> => {
    try {
      connectionStatus.value = 'checking'
      // console.log('Checking MDVR connection status...')

      const response = await $fetch('/api/mdvr/token', {
        method: 'GET'
      })

      // console.log('MDVR token response:', response)
      const isConnected = response?.data?.isConnected === true
      connectionStatus.value = isConnected ? 'connected' : 'disconnected'
      lastChecked.value = new Date()

      console.log('MDVR connection status:', connectionStatus.value)
      return isConnected
    } catch (error: any) {
      console.error('Error checking MDVR connection:', error)
      connectionStatus.value = 'disconnected'
      lastChecked.value = new Date()
      return false
    }
  }

  // Computed property that combines token existence and connection status
  // Don't show as disconnected during checking phase if we have a token
  const isMdvrConnected = computed(() => {
    const hasToken = hasMdvrToken.value
    const status = connectionStatus.value

    // If we have a token and status is 'connected', return true
    // If we have a token and status is 'checking', keep previous state (don't show false during check)
    // If we have a token and status is 'disconnected', return false
    // If we don't have a token, return false

    let result = false
    if (hasToken) {
      if (status === 'connected') {
        result = true
      } else if (status === 'checking') {
        // During checking, maintain the connected state if we have a token
        // This prevents flickering in the UI
        result = true
      } else {
        // status === 'disconnected'
        result = false
      }
    }



    return result
  })

  // Auto-check connection status when user data changes
  watch(data, async (newData) => {
    if (newData && hasMdvrToken.value) {
      await checkConnectionStatus()
    } else {
      connectionStatus.value = 'disconnected'
    }
  }, { immediate: true })

  // Refresh connection status (useful after login/logout)
  const refreshConnectionStatus = async () => {
    if (hasMdvrToken.value) {
      return await checkConnectionStatus()
    } else {
      connectionStatus.value = 'disconnected'
      return false
    }
  }

  return {
    hasMdvrToken,
    connectionStatus: readonly(connectionStatus),
    lastChecked: readonly(lastChecked),
    isMdvrConnected,
    checkConnectionStatus,
    refreshConnectionStatus
  }
}
