// Create new file: workers/marker-worker.ts
self.onmessage = (e) => {
  const { devices, positions, mapSettings } = e.data
  
  const markersData = devices.map(device => {
    const position = positions.find(p => p.deviceId === device.id)
    if (!position) return null
    
    const isDeviceActive = device.telematics?.device_data?.active === 1
    const shouldShowDevice = (
      isDeviceActive && (
        (device.status === 'online' && mapSettings.vesselStatus.showOnline) ||
        (device.status === 'offline' && mapSettings.vesselStatus.showOffline) ||
        (device.status === 'waiting' && mapSettings.vesselStatus.showWaiting)
      )
    )
    
    if (!shouldShowDevice) return null
    
    return {
      id: device.id,
      lat: device.lat || position.latitude,
      lng: device.lng || position.longitude,
      course: position.course || 0,
      status: device.status,
      type: device.type
    }
  }).filter(<PERSON><PERSON><PERSON>)
  
  self.postMessage(markersData)
}