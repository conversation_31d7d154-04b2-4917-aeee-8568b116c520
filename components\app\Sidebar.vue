<script setup lang="ts">
import {computed} from "vue";
import {initFlowbite} from "flowbite";
import type {DrawerInterface} from "flowbite";
import {useAuthStore} from "~/store/auth";
import iconBuilding from '~/components/icon/Building.vue';
import iconFolder from '~/components/icon/Folder.vue';

const $auth = useAuthStore()
let drawerSidebar: DrawerInterface | null = null
const {data} = useAuth();


const emit = defineEmits(['on-mounted', 'on-click-close-sidebar'])

const setupDrawer = async (targetId: string) => {
  const {Drawer} = await import('flowbite');
  const $target = document.getElementById(targetId)
  return new Drawer($target)
}

onMounted(async () => {
  const {initFlowbite} = await import('flowbite');
  initFlowbite()
  drawerSidebar = await setupDrawer('targetDrawerSidebar')
  emit('on-mounted', drawerSidebar)
})

const routePath = computed((): string => {
  return useRoute().path
})

const getTriggerClass = (isActive: boolean, hoverClass: string): string => {
  const baseClass = 'py-2 px-3 transition flex items-center justify-between rounded-md w-full font-medium'
  const activeClass = isActive ? 'bg-[#FDEBEB] text-[#EF3434] stroke-[#EF3434]' : 'bg-none text-black stroke-gray-900'
  const hoverEffect = isActive ? '' : hoverClass
  return `${baseClass} ${activeClass} ${hoverEffect}`
}

const getColor = (destination: string) => {
  return routePath.value.startsWith(destination) ? 'stroke-primary-500' : 'stroke-gray-900'
}

// Navigation links with conditional Evidence menu
const navigationLinks = computed(() => {
  const { isMdvrConnected } = useMdvrConnection()



  const allLinks = [
    {
      id: 'menuDashboard',
      name: 'Dashboard',
      destination: '/dashboard',
      icon: iconBuilding,
      isVisible: true
    },
    {
      id: 'menuEvidence',
      name: 'Evidence',
      destination: '/evidence',
      icon: iconFolder,
      isVisible: isMdvrConnected.value
    },
  ]

  return allLinks.filter(link => link.isVisible)
})

</script>

<template>
  <aside
      id="targetDrawerSidebar"
      class="w-full h-full md:w-[280px] fixed top-0 overflow-y-auto py-10 px-4 z-40 border-r bg-white transition-transform -translate-x-full md:translate-x-0"
  >
    <div class="flex flex-col justify-between h-full">
      <div>
        <div class="mb-8 px-3 flex items-center justify-between">
          <div class="flex items-center">
            <a class="text-xl font-bold ml-2">Project Base</a>
          </div>
          <general-icon-button class="block border-transparent md:hidden" @click="emit('on-click-close-sidebar')">
            <template #icon>
              <icon-close class="stroke-gray-500"/>
            </template>
          </general-icon-button>
        </div>
        <div class="flex flex-col border-y py-6 px-4">
          <general-dropdown id="dropdown-profile">
            <template #activator>
              <div class="flex flex-row items-center justify-between cursor-pointer">
                <div class="flex flex-col">
                  <a class="font-semibold">{{ data.data?.name }}</a>
                  <a v-if="data.data?.name !== 'Super Admin'" style="color: #6E6666; font-size: 12px">{{ data.data?.role?.name }}</a>
                </div>
                <icon-arrow-down class="fill-black"/>
              </div>
            </template>
            <template #content>
              <div class="w-60 px-4 py-2">
                <div class="py-2.5 px-1 flex items-center cursor-pointer hover:bg-gray-100 rounded-lg" @click="$auth.logout()">
                  <div class="flex">
                    <icon-logout class="stroke-red-500"/>
                    <a class="text-red-500 pl-2">Logout</a>
                  </div>
                </div>
              </div>
            </template>
          </general-dropdown>
        </div>
        <div class="py-10">
          <ul class="space-y-3">
            <li v-for="link in navigationLinks" :key="link.id">
              <app-navigation-link :id="link.id" :name="link.name" :destination="link.destination">
                <template #icon>
                  <component :is="link.icon" :class="getColor(link.destination)"/>
                </template>
              </app-navigation-link>
            </li>

          </ul>
        </div>
      </div>
    </div>
  </aside>
</template>

<style scoped></style>
