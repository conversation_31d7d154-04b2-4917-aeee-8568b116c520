<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: [Number, Object as () => null],
    default: null
  },
  step: {
    type: [Number, Object as () => null],
    default: null
  },
  steps: {
    type: Array as () => { text: string, isAllowed: boolean }[],
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:model-value'])

const activeStep = computed((): number => {
  return props.modelValue ?? props.step ?? 1
})

const stepClass = (step: number, isActive: boolean) => {
  const baseClass = 'font-semibold transition text-sm'
  const cursor = props.readonly ? 'select-none' : 'cursor-pointer underline'
  const text = step <= activeStep.value ? 'text-primary-500' : !isActive ? 'text-gray-400 !cursor-not-allowed' : 'text-gray-900'
  return `${baseClass} ${cursor} ${text}`
}

function onClickStep (isAllowed: boolean, index: number) {
  if (props.readonly) return
  if (isAllowed) emit('update:model-value', index + 1)
}
</script>

<template>
  <ol class="flex w-full">
    <li v-for="(step, i) in props.steps" class="overflow-hidden flex-1 flex flex-col items-center">
      <div class="mb-2 w-full flex items-center">
        <span class="w-full h-0.5" :class="i > 0 ? (i <= activeStep - 1 ? 'bg-primary-500' : 'bg-gray-200') : ''"></span>

        <icon-stepper-active v-if="i === activeStep - 1" :size="40" />
        <icon-stepper-done v-else-if="i <= activeStep - 1" :size="40" />
        <icon-stepper-inactive v-else :size="40" />

        <span class="w-full h-0.5" :class="i < props.steps.length - 1 ? (i < activeStep - 1 ? 'bg-primary-500' : 'bg-gray-200') : ''"></span>
      </div>

      <p
        :class="stepClass(i + 1, step.isAllowed)"
        @click="onClickStep(step.isAllowed, i)"
      >
        {{ step.text }}
      </p>
    </li>
  </ol>
</template>

<style scoped>

</style>