import { defineEventHandler, readBody } from 'h3'
import { db } from '../../database/drizzle'
import { integrationTokens } from '../../database/schema'
import { requireUserSession } from '../../utils/session'
import { add } from 'date-fns'
import { eq, and } from 'drizzle-orm'

export default defineEventHandler(async (event) => {
	const session = await requireUserSession(event)
	const body = await readBody(event)

	const { token, pid, guid, username } = body

	// Calculate expiry date (7 days from now)
	const expiry = add(new Date(), { days: 7 })

	// Delete any existing token for this user
	await db.delete(integrationTokens)
		.where(
			and(
				eq(integrationTokens.userId, session.mdvr?.id ?? ''),
				eq(integrationTokens.provider, 'mdvr')
			)
		)

	// Insert new token
	await db.insert(integrationTokens).values({
		userId: session.telematics?.id ?? '',
		provider: 'mdvr',
		token,
		pid,
		guid,
		username,
		expiry
	})

	return {
		status: 'success',
		message: 'MDVR token saved successfully'
	}
})