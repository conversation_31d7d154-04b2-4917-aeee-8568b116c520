<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <div class="flex flex-col items-center space-y-4">
    <div
      class="w-12 aspect-square rounded-full bg-primary-100 flex items-center justify-center border-[6px] border-primary-50 stroke-primary-500">
      <slot name="icon" />
      <icon-package v-if="!$slots.icon" size="20"/>
    </div>

    <div class="flex flex-col items-center space-y-1">
      <p class="text-sm font-semibold text-gray-900">{{ props.title }}</p>
      <p class="text-sm text-gray-500">{{ props.subtitle }}</p>
    </div>
  </div>
</template>

<style scoped></style>