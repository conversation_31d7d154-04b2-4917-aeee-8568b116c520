import { $fetch } from 'ofetch'
import { MDVRDevice } from '~/types/device'

const MDVR_BASE_URL = 'https://mdvr.transtrack.id/vss'

interface MDVRLoginResponse {
	status: number
	msg: string
	error: string | null
	data: {
		token: string
		pid: string
		guid: string
		username: string
		permission: string
		allPermission: string
		logo: string
		roleNumber: string
		sysVersion: string
		effectiveDate: string | null
		effectiveDuration: string | null
		ssid: string | null
		allowDevMax: number
		createtime: string
		modifytime: string
		roleid: string
		isenable: number
		configinfo: string
		basicsinfo: string | null
	}
	count: number
}

interface MDVRDevicesResponse {
	status: number
	msg: string
	error: string | null
	data: {
		totalCount: number
		pageCount: number
		fromCount: number
		toCount: number
		totalNum: number
		pageNum: number
		dataList: MDVRDevice[]
	}
}

export async function loginToMDVR(username: string, password: string) {
	try {
		const response = await $fetch<MDVRLoginResponse>(`${MDVR_BASE_URL}/user/login.action`, {
			method: 'POST',
			body: {
				username,
				password,
				terminal: "2",
				ssid: "",
				verifyCode: "",
				remeber: "true",
				scheme: "https",
				lang: "en"
			}
		})

		if (response.status !== 10000) {
			throw new Error(response.msg || 'Failed to login to MDVR')
		}

		return {
			token: response.data.token,
			pid: response.data.pid,
			guid: response.data.guid,
			username: response.data.username,
			permission: response.data.permission,
			roleNumber: response.data.roleNumber
		}
	} catch (error: any) {
		throw new Error(`MDVR login failed: ${error.message}`)
	}
}

export async function getDevicesFromMDVR(token: string): Promise<MDVRDevice[]> {
	try {
		const response = await $fetch<MDVRDevicesResponse>(`${MDVR_BASE_URL}/vehicle/findAll.action`, {
			method: 'POST',
			body: {
				token: token
			}
		})

		if (response.status !== 10000) {
			throw new Error(response.msg || 'Failed to fetch MDVR devices')
		}

		return response.data.dataList
	} catch (error: any) {
		throw new Error(`Failed to fetch MDVR devices: ${error.message}`)
	}
}
