<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import RangeDatePicker from '~/components/general/RangeDatePicker.vue'
import { format } from 'date-fns'
import { toast } from "vue3-toastify"
import IconEye from '~/components/icon/Eye.vue'
import IconFileSearch from '~/components/icon/FileSearch.vue'

import GeneralIconButton from '~/components/general/IconButton.vue'
import ViewEvidenceModal from '~/components/evidence/ViewEvidenceModal.vue'
import MDVRAuthOverlay from '~/components/evidence/MDVRAuthOverlay.vue'
import type { ElementEvent } from '~/types/element'

const startDate = ref(new Date())
const endDate = ref(new Date())
const evidences = ref<any[]>([])
const loading = ref(false)

const itemsPerPage = ref(12)
const currentPage = ref(1)

const paginatedEvidences = computed(() => {
	const start = (currentPage.value - 1) * itemsPerPage.value
	const end = start + itemsPerPage.value
	return evidences.value.slice(start, end)
})

const totalPages = computed(() => {
	return Math.ceil(evidences.value.length / itemsPerPage.value)
})

const nextPage = () => {
	if (currentPage.value < totalPages.value) {
		currentPage.value++
	}
}

const prevPage = () => {
	if (currentPage.value > 1) {
		currentPage.value--
	}
}



const formatDate = (date: Date) => {
	return format(date, 'yyyy-MM-dd+HH:mm:ss')
}

const fetchEvidences = async () => {
	try {
		loading.value = true
		const response = await $fetch('/api/mdvr/evidence', {
			method: 'POST',
			body: {
				startTime: formatDate(startDate.value),
				endTime: formatDate(endDate.value)
			}
		})

		if (response?.status === 10000 && Array.isArray(response?.data?.list)) {
			evidences.value = response.data.list.map((item: any) => {
				return {
					...item,
					imageUrl: item.imageUrl
				}
			})
			console.log('Processed evidence items:', evidences.value)
		} else {
			console.log('No valid evidence data found')
			evidences.value = []
			toast.error(response?.msg || 'No evidence data available')
		}
	} catch (error: any) {
		console.error('Error fetching evidences:', error)
		evidences.value = []

		// Check if it's a 401 error or MDVR authentication related error
		const isAuthError = error.statusCode === 401 ||
							error.status === 401 ||
							error.message?.includes('Unauthorized') ||
							error.message?.includes('No valid MDVR token') ||
							error.data?.message?.includes('Unauthorized')

		if (isAuthError) {
			// Refresh connection status to reflect disconnected state
			const { refreshConnectionStatus } = useMdvrConnection()
			await refreshConnectionStatus()

			// Show MDVR authentication overlay
			mdvrAuthOverlay.value?.show()
			toast.warning('MDVR authentication required. Please login to continue.')
		} else {
			toast.error(error.message || 'An unexpected error occurred')
		}
	} finally {
		loading.value = false
	}
}

onMounted(() => {
	// Set date range to last 24 hours
	const now = new Date()
	endDate.value = now
	startDate.value = new Date(now.getTime() - (1 * 60 * 60 * 1000))
	fetchEvidences()
})

const onDateRangeChange = (dates: Date[]) => {
	if (dates && dates.length === 2) {
		startDate.value = dates[0]
		endDate.value = dates[1]
		fetchEvidences()
	}
}

const selectedEvidence = ref<any>(null)
const viewEvidenceModal = ref<ElementEvent | null>(null)
const mdvrAuthOverlay = ref<ElementEvent | null>(null)

const viewEvidence = (evidence: any) => {
	selectedEvidence.value = evidence
	const modal = document.getElementById('modal-view-evidence')
	if (modal) {
		modal.classList.remove('hidden')
		modal.classList.add('animate-fade-in')
	}
}

const onModalMounted = (event: ElementEvent) => {
	viewEvidenceModal.value = event
}

const onMDVRAuthOverlayMounted = (event: ElementEvent) => {
	mdvrAuthOverlay.value = event
}

const handleMDVRAuthSuccess = async () => {
	// Refresh MDVR connection status
	const { refreshConnectionStatus } = useMdvrConnection()
	await refreshConnectionStatus()

	// Refresh the evidence data after successful authentication
	await fetchEvidences()
	toast.success('MDVR authentication successful! Evidence data refreshed.')
}
</script>

<template>
	<div class="h-full w-full overflow-y-auto bg-gray-50 p-6">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="text-2xl font-bold text-gray-900">Evidence</h1>
			<p class="mt-2 text-sm text-gray-600">View and manage evidence from your devices</p>
		</div>

		<!-- Filters -->
		<div class="bg-white rounded-lg shadow p-4 mb-6">
			<div class="flex flex-col md:flex-row gap-4">
				<RangeDatePicker id="evidence-date-range" label="Date Range" :model-value="[startDate, endDate]"
					@update:model-value="onDateRangeChange" class="w-full md:w-80" />
			</div>
		</div>

		<!-- Content -->
		<div class="bg-white rounded-lg shadow p-6 mb-12">
			<!-- Loading State -->
			<div v-if="loading" class="flex justify-center items-center min-h-[400px]">
				<div class="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
			</div>

			<!-- Empty State -->
			<div v-else-if="evidences.length === 0" class="flex flex-col items-center justify-center min-h-[400px]">
				<icon-file-search class="w-16 h-16 text-gray-400 mb-4" />
				<h3 class="text-lg font-medium text-gray-900">No Evidence Found</h3>
				<p class="mt-1 text-sm text-gray-500">Try adjusting your search filters or selecting a different date
					range.</p>
			</div>

			<!-- Grid View -->
			<div v-else>
				<div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
					<div v-for="evidence in paginatedEvidences" :key="evidence.id"
						class="bg-white border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
						<!-- Image Preview -->
						<div class="relative aspect-video bg-gray-100">
							<NuxtImg :src="evidence.imageUrl" :alt="'Evidence from ' + evidence.deviceName"
								class="w-full h-full object-cover" />
						</div>

						<!-- Evidence Info -->
						<div class="p-4">
							<div class="mb-2">
								<p class="text-sm font-medium text-gray-900">
									{{ format(new Date(evidence.alarmTime), 'dd/MM/yyyy HH:mm:ss') }}
								</p>
								<p class="text-sm text-gray-600">{{ evidence.deviceName }}</p>
							</div>

							<div class="flex items-center justify-between">
								<span class="text-sm text-gray-500">
									{{ evidence.speed ? `${evidence.speed} Km/h` : 'N/A' }}
								</span>
								<general-icon-button color="primary" @on-click="viewEvidence(evidence)"
									tooltip="View Evidence">
									<template #icon>
										<icon-eye class="w-4 h-4" />
									</template>
								</general-icon-button>
							</div>
						</div>
					</div>
				</div>

				<!-- Pagination -->
				<div class="mt-6 flex items-center justify-between border-t border-gray-200 pt-4">
					<button @click="prevPage" :disabled="currentPage === 1"
						class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
						Previous
					</button>
					<span class="text-sm text-gray-700">
						Page {{ currentPage }} of {{ totalPages }}
					</span>
					<button @click="nextPage" :disabled="currentPage === totalPages"
						class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50">
						Next
					</button>
				</div>
			</div>
		</div>
		<evidence-view-evidence-modal
			:evidence="selectedEvidence"
			@on-mounted="onModalMounted"
		/>

		<!-- MDVR Authentication Overlay -->
		<MDVRAuthOverlay
			@mounted="onMDVRAuthOverlayMounted"
			@auth-success="handleMDVRAuthSuccess"
		/>

	</div>
</template>
