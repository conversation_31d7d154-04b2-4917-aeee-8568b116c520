<template>
  <div class="device-list">
    <VirtualDeviceList
      :items="filteredDevices"
      :item-height="60"
      :buffer="15"
    >
      <template #item="{ device }">
        <DeviceItem 
          :device="device"
          :visible="isDeviceVisible(device)"
          :selected="store.selectedDevice?.id === device.id"
          :following="store.isFollowingDevice && store.selectedDevice?.id === device.id"
          :position="store.getDevicePosition(device)"
          @toggle-visibility="store.toggleDeviceVisibility(device.id)"
          @toggle-follow="store.toggleFollowDevice(device)"
          @click="store.selectDevice(device)"
        />
      </template>
    </VirtualDeviceList>
  </div>
</template>

<script setup lang="ts">
import { useMapStore } from '~/store/map'
import { computed } from 'vue'

const store = useMapStore()

// Memoize filtered devices
const filteredDevices = computed(() => {
  return store.filteredDevices
})

const isDeviceVisible = (device: any) => {
  if (!device) return false
  return device.telematics 
    ? device.telematics.device_data?.active === 1 
    : store.visibleDevices.has(device.id)
}
</script>

<style scoped>
.device-list {
  height: 100%;
  overflow: hidden;
  contain: strict;
}
</style>
