<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <div>
    <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 29 28" fill="none">
      <path
        d="M10.9987 18.0833H9.2487C7.62054 18.0833 6.80646 18.0833 6.14403 18.2843C4.65256 18.7367 3.48541 19.9039 3.03298 21.3953C2.83203 22.0578 2.83203 22.8718 2.83203 24.5M17.4154 8.75C17.4154 11.6495 15.0649 14 12.1654 14C9.26587 14 6.91536 11.6495 6.91536 8.75C6.91536 5.8505 9.26587 3.5 12.1654 3.5C15.0649 3.5 17.4154 5.8505 17.4154 8.75ZM13.332 24.5L16.9503 23.4662C17.1235 23.4167 17.2102 23.392 17.291 23.3549C17.3627 23.3219 17.4309 23.2818 17.4946 23.2351C17.5663 23.1824 17.63 23.1187 17.7574 22.9913L25.2904 15.4584C26.0959 14.6529 26.0959 13.3471 25.2904 12.5416C24.485 11.7362 23.1791 11.7362 22.3737 12.5417L14.8407 20.0746C14.7133 20.2021 14.6496 20.2658 14.597 20.3374C14.5503 20.4011 14.5101 20.4693 14.4772 20.5411C14.4401 20.6219 14.4153 20.7085 14.3658 20.8818L13.332 24.5Z"
        stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  </div>
</template>

<style scoped></style>