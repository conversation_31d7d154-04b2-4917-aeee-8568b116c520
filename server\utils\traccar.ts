const TRACCAR_URL = useRuntimeConfig().traccarURL

interface SessionData {
    jsessionid?: string;
}

const sessionStore = new Map<string, SessionData>();

export async function loginToTraccar(email: string, password: string): Promise<{sessionId: string, user: any}> {
    const body = new URLSearchParams();
    body.append('email', email);
    body.append('password', password);

    const bodyString = body.toString();

    const response = await $fetch.raw(`${TRACCAR_URL}/api/session`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Host': new URL(TRACCAR_URL).host,
            'Content-Length': bodyString.length.toString(),
        },
        body: bodyString
    });

    const jsessionid = getCookieFromSetCookieHeader(response.headers.get('set-cookie'));

    if (!jsessionid) {
        throw createError({statusCode: 401, statusMessage: 'Login Failed'});
    }

    const sessionId = generateSessionId()
    sessionStore.set(sessionId, {jsessionid});

    return {sessionId, user: response._data};
}

export async function getSession(sessionId: string): Promise<any> {
    const session = sessionStore.get(sessionId);
    if (!session || !session.jsessionid) {
        throw createError({statusCode: 401, statusMessage: 'Unauthorized'});
    }

    const url = new URL(`${TRACCAR_URL}/api/session`);

    return await $fetch(`${TRACCAR_URL}/api/session`, {
        method: 'GET',
        headers: {
            'Cookie': `JSESSIONID=${session.jsessionid}`,
            'Host': url.host,
        }

    });
}

export function logoutFromTraccar(sessionId: string) {
    sessionStore.delete(sessionId)
}

export async function getDevicesFromTraccar(sessionId: string): Promise<any> {
    const session = sessionStore.get(sessionId)
    if (!session || !session.jsessionid) {
        throw createError({statusCode: 401, statusMessage: 'Unauthorized'});
    }

    const url = new URL(`${TRACCAR_URL}/api/devices`);

    return await $fetch(`${TRACCAR_URL}/api/devices`, {
        method: 'GET',
        headers: {
            'Cookie': `JSESSIONID=${session.jsessionid}`,
            'Host': url.host,
        }
    });
}

export async function getPositionsFromTraccar(sessionId: string): Promise<any> {
    const session = sessionStore.get(sessionId)
    if (!session || !session.jsessionid) {
        throw createError({statusCode: 401, statusMessage: 'Unauthorized'});
    }

    const url = new URL(`${TRACCAR_URL}/api/positions`);

    return await $fetch(`${TRACCAR_URL}/api/positions`, {
        method: 'GET',
        headers: {
            'Cookie': `JSESSIONID=${session.jsessionid}`,
            'Host': url.host,
        }
    });
}

export async function getGroupsFromTraccar(sessionId: string): Promise<any> {
    const session = sessionStore.get(sessionId)
    if (!session || !session.jsessionid) {
        throw createError({statusCode: 401, statusMessage: 'Unauthorized'});
    }

    const url = new URL(`${TRACCAR_URL}/api/groups`);

    return await $fetch(`${TRACCAR_URL}/api/groups`, {
        method: 'GET',
        headers: {
            'Cookie': `JSESSIONID=${session.jsessionid}`,
            'Host': url.host,
        }
    });
}


function getCookieFromSetCookieHeader(header: string | null): string | null {
    if (!header) return null;

    const cookies = header.split(';').map(c => c.trim());
    for (const cookie of cookies) {
        if (cookie.startsWith('JSESSIONID=')) {
            return cookie.split('=')[1];
        }
    }
    return null;
}

function generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}