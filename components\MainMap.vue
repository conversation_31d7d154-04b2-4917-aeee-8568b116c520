<template>
  <div class="absolute inset-0 z-0" :class="{ 'pointer-events-none': store.isSidebarOpen && window.innerWidth < 768 }">
    <div id="map" class="absolute inset-0" />
    <WindyControl />
  </div>
  <div class="absolute bottom-4 right-4 z-[95]">
    <div class="border-transparent rounded-sm bg-gray-400 bg-opacity-50 p-2 space-y-2">
      <general-icon-button :bordered="false" color="" class="bg-white w-[36px] h-[36px]" @on-click="zoomIn()">
        <template #icon>
          <icon-zoom-in class="fill-blue-500" />
        </template>
      </general-icon-button>
      <general-icon-button :bordered="false" color="" class="bg-white w-[36px] h-[36px]" @on-click="zoomOut()">
        <template #icon>
          <icon-zoom-out class="fill-blue-500" />
        </template>
      </general-icon-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as L from 'leaflet'
import type { UnifiedDevice } from '~/types/device'
import type { Position } from '~/types/position'
import 'leaflet-control-geocoder'
import 'leaflet-control-geocoder/dist/Control.Geocoder.css'
import WindyControl from './WindyControl.vue'

// Add trail polyline type
type TrailPolyline = L.Polyline & { deviceId?: number }
import { useMapStore } from '~/store/map'
import 'leaflet/dist/leaflet.css'
import 'leaflet.markercluster'
import 'leaflet.markercluster/dist/MarkerCluster.css'
import 'leaflet.markercluster/dist/MarkerCluster.Default.css'
import 'leaflet-rotatedmarker'

// Types
type RotatedMarker = L.Marker & {
  setRotationAngle: (angle: number) => void;
  setRotationOrigin: (origin: string) => void;
}

type MarkerClusterGroupType = L.MarkerClusterGroup & { clearLayers: () => void }

// Add window reference for TypeScript
const window = globalThis.window

const { response } = useWebSocket()

const store = useMapStore()
const mapInstance = ref<L.Map | null>(null)
const markerCluster = ref<MarkerClusterGroupType | null>(null)
const baseLayers = ref<{ [key: string]: L.TileLayer }>({})
const overlayLayers = ref<{ [key: string]: L.TileLayer }>({})
const layerControl = ref<L.Control.Layers | null>(null)
const deviceMarkers = ref<Map<number, RotatedMarker>>(new Map())
const deviceTrails = ref<Map<number, TrailPolyline>>(new Map())
const windyInstance = ref<any>(null)

// Add TypeScript interface for Windy
declare global {
  interface Window {
    W: any
  }
}



const zoom = ref(6)
const mapCenter = ref<[number, number]>([47.21322, -1.559482])
const { $createMarkerCluster } = useNuxtApp()
const router = useRouter()


// Add createMarker helper function
const createMarker = (device: UnifiedDevice, position: Position): RotatedMarker => {
  const marker = L.marker(
    [device.lat || position.latitude, device.lng || position.longitude],
    {
      icon: getDeviceIcon(device),
      rotationAngle: (position.course || 0),
      rotationOrigin: 'center center'
    }
  ) as RotatedMarker

  // Only bind popup if labels are enabled and marker is clicked
  if (store.mapSettings.vesselElement.showLabel) {
    marker.on('click', () => {
      const fixTime = position.fixTime ? formatDate(position.fixTime) : 'N/A'
      const groupName = device.groupId ? store.groups.find(g => g.id === device.groupId)?.name : 'Ungrouped'
      const category = device.traccar?.category || 'N/A'

      if (marker.getPopup()) return

      marker.bindPopup(`
        <div class="p-0 min-w-[300px]">
        <div class="relative h-[200px] bg-gray-200 mb-4">
          <NuxtImg src="${getDeviceImage(device)}" 
             class="w-full h-full object-cover" 
             alt="${device.name}"
             onerror="this.style.opacity='0.5';this.style.backgroundColor='#f3f4f6'"
          />
          <div class="absolute top-4 left-4 flex items-center gap-2">
          <div class="flex items-center gap-1 px-2 py-1 text-xs bg-white rounded-full">
            <svg class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke-width="2"/>
            </svg>
            <span>${device.type || 'Tugboat'}</span>
          </div>
          <div class="flex items-center gap-1 px-2 py-1 text-xs bg-white rounded-full">
            <svg class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M2 22l20-20M17 13l5 5M13 17l5 5M14 10l2-2M10 14l2-2M6 18l2-2" stroke-width="2"/>
            </svg>
            <span>${device.source || 'GSM'}</span>
          </div>
          </div>
            <button class="close-btn absolute top-4 right-4 p-1 rounded-full bg-white/80 hover:bg-white">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M6 18L18 6M6 6l12 12" stroke-width="2" stroke-linecap="round"/>
          </svg>
          </button>
        </div>
        <div class="px-4 pb-4">
          <div class="flex items-center gap-2 mb-4">
          <div class="flex items-center gap-1">
            <svg class="w-4 h-4 text-gray-400" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M3 7h18M3 12h18M3 17h18" stroke-width="2"/>
            </svg>
            <span class="text-xl font-bold">${device.name}</span>
          </div>
          </div>
          
          <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Unit Status</span>
            <span class="px-2 py-0.5 text-xs rounded-full ${device.status === 'online' ? 'bg-green-100 text-green-800' :
          device.status === 'offline' ? 'bg-red-100 text-red-800' :
            'bg-yellow-100 text-yellow-800'
        }">${device.status}</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Engine Status</span>
            <span class="px-2 py-0.5 text-xs rounded-full bg-green-100 text-green-800">On</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Battery Voltage</span>
            <span class="text-sm font-medium">${device.telematics?.device_data?.battery || 'N/A'} v</span>
          </div>
          </div>

          <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Last Update</span>
            <span class="text-sm font-medium">${formatDate(position.fixTime)}</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Wind Speed</span>
            <span class="text-sm font-medium">${position.speed || '0.00'} km/h</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Wind Direction</span>
            <span class="text-sm font-medium">${position.course || '0.00'}°</span>
          </div>
          </div>

          <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Running Hour</span>
            <span class="text-sm font-medium">${device.telematics?.device_data?.running_hours || '0.00'}</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Speed</span>
            <span class="text-sm font-medium">${position.speed || '0.00'} Knot</span>
          </div>
          </div>

          <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Heading</span>
            <span class="text-sm font-medium">${position.course || '0.00'}°</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Altitude</span>
            <span class="text-sm font-medium">${formatAltitude(position.altitude) || '0'}</span>
          </div>
          </div>

          <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Latitude</span>
            <span class="text-sm font-medium">${formatCoordinate(position.latitude, true)}°</span>
          </div>
          <div class="flex flex-col items-start">
            <span class="text-gray-500 text-xs mb-1">Longitude</span>
            <span class="text-sm font-medium">${formatCoordinate(position.longitude, false)}°</span>
          </div>
          </div>

          <div class="mb-4">
          <span class="text-gray-500 text-xs mb-1 block">Address</span>
          <span class="text-sm font-medium">${device.telematics?.device_data?.address || 'N/A'}</span>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <button class="dashboard-btn flex items-center justify-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" stroke-width="2"/>
            </svg>
            Dashboard
            </button>
            <button class="report-btn flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" stroke-width="2"/>
            </svg>
            View Report
            </button>
          </div>
        </div>
        </div>
      `, {
        maxWidth: 400,
        className: 'custom-popup'
      }).openPopup()
    })
  }

  return marker
}

const getDeviceImage = (device: UnifiedDevice) => {
  if (device.telematics?.device_data?.image) {
    return device.telematics.device_data.image
  }
  // Return a data URI for a gradient background as fallback
  return `data:image/svg+xml,${encodeURIComponent(`
    <svg width="400" height="200" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#2563eb;stop-opacity:0.2" />
          <stop offset="100%" style="stop-color:#1e40af;stop-opacity:0.3" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)"/>
    </svg>
  `)}`
}

// Cache icons to prevent recreation
const iconCache = new Map<string, L.Icon>()

const getDeviceIcon = (device: UnifiedDevice): L.Icon => {
  const key = `${device.status}-${device.type}`
  if (iconCache.has(key)) {
    return iconCache.get(key)!
  }

  const icon = L.icon({
    iconUrl: `/api/marker/${device.id}?name=${encodeURIComponent(device.name)}&status=${device.status}&source=${device.source}`,
    iconSize: [100, 70],
    iconAnchor: [50, 50],
    popupAnchor: [0, -40],
    className: `device-icon device-${device.status}` // Use CSS classes for status
  })

  iconCache.set(key, icon)
  return icon
}

function navigateToReport(deviceId: number) {
  router.push(`/dashboard/device/${deviceId}/report`)
}

const formatDate = (date: string | undefined): string => {
  if (!date) return 'N/A'
  return new Date(date).toLocaleString()
}

const updateDeviceTrail = (deviceId: number, tail: string) => {
  if (!mapInstance.value || !tail) return

  // Remove existing trail
  if (deviceTrails.value.has(deviceId)) {
    deviceTrails.value.get(deviceId)?.remove()
  }

  try {
    const device = store.devices.find(d => d.id === deviceId)
    if (!device?.telematics?.device_data) return

    // Parse tail data
    const trailCoords = tail.split(';').map(coord => {
      const [lat, lng] = coord.split('/')
      if (!lat || !lng) {
        console.warn(`Invalid coordinates in tail data for device ${deviceId}`)
        return null
      }
      const latNum = parseFloat(lat)
      const lngNum = parseFloat(lng)
      if (isNaN(latNum) || isNaN(lngNum)) {
        console.warn(`Invalid coordinate values in tail data for device ${deviceId}`)
        return null
      }
      return [latNum, lngNum]
    }).filter((coord): coord is [number, number] => coord !== null)

    if (trailCoords.length === 0) return

    // Limit trail length based on device settings
    const tailLength = device.telematics.device_data.tail_length || 5
    const limitedCoords = trailCoords.slice(0, tailLength)

    // Create new trail with device color and settings
    const trail = L.polyline(limitedCoords.map(([lat, lng]) => L.latLng(lat, lng)), {
      color: device.telematics.device_data.tail_color || '#33cc33',
      weight: store.mapSettings.trailSettings.width,
      opacity: store.mapSettings.trailSettings.opacity,
      dashArray: store.mapSettings.vesselElement.showDotTrail ? '5, 10' : null,
      lineCap: 'round',
      lineJoin: 'round',
      smoothFactor: 1,
      className: 'vessel-trail'
    }) as TrailPolyline

    trail.deviceId = deviceId
    trail.addTo(mapInstance.value)
    deviceTrails.value.set(deviceId, trail)
  } catch (error) {
    console.error(`Error updating trail for device ${deviceId}:`, error)
  }
}


// Add refreshMarkers helper function
const refreshMarkers = (skipBoundsFit: boolean = false) => {
  if (!mapInstance.value || !markerCluster.value) return

  const newMarkers = new Map<number, RotatedMarker>()
  const visibleMarkers: RotatedMarker[] = []

  markerCluster.value.removeLayers(Array.from(deviceMarkers.value.values()))

  const markersToAdd: RotatedMarker[] = []

  store.devices.forEach(device => {
    const shouldShowDevice = store.visibleDevices.has(device.id) && (
      (device.status === 'online' && store.mapSettings.vesselStatus.showOnline) ||
      (device.status === 'offline' && store.mapSettings.vesselStatus.showOffline) ||
      (device.status === 'waiting' && store.mapSettings.vesselStatus.showWaiting)
    )

    if (shouldShowDevice) {
      const position = store.getDevicePosition(device)
      const existingMarker = deviceMarkers.value.get(device.id)

      let marker: RotatedMarker | undefined

      if (existingMarker) {
        existingMarker.setLatLng([
          device.lat || position.latitude,
          device.lng || position.longitude
        ])
        existingMarker.setIcon(getDeviceIcon(device))
        existingMarker.setRotationAngle((position.course || 0))
        marker = existingMarker
      } else {
        marker = createMarker(device, position)
        markersToAdd.push(marker)
      }

      if (marker) {
        newMarkers.set(device.id, marker)
        visibleMarkers.push(marker)
      }
    }
  })

  if (markersToAdd.length > 0) {
    markerCluster.value.addLayers(markersToAdd)
  }

  deviceMarkers.value = newMarkers

  if (!store.isFollowingDevice && !skipBoundsFit && visibleMarkers.length > 0) {
    const group = L.featureGroup(visibleMarkers)
    mapInstance.value.fitBounds(group.getBounds(), { padding: [50, 50] })
  }
}


const initializeMap = () => {
  if (!mapInstance.value) {
    try {
      mapInstance.value = L.map('map', {
        zoomControl: false,
        rotate: true,
        attributionControl: true,
        shiftKeyRotate: true,
        touchRotate: true,
        rotationControl: false,
        // Add these options for Windy
        center: mapCenter.value,
        zoom: zoom.value,
        zoomAnimation: true,
        preferCanvas: true
      })

      mapInstance.value.on('dragstart', () => {
        store.isFollowingDevice = false
      })
      mapInstance.value.on('zoomstart', () => {
        store.isFollowingDevice = false
      })

      // Initialize base layers
      baseLayers.value = {
        'OpenStreetMap': L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/">OpenStreetMap</a> contributors'
        }),
        'Satellite': L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
          attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
        })
      }

      // Initialize overlay layers
      overlayLayers.value = {
        'Sea Marks': L.tileLayer('https://tiles.openseamap.org/seamark/{z}/{x}/{y}.png', {
          attribution: 'Map data: &copy; <a href="https://www.openseamap.org/">OpenSeaMap</a> contributors'
        }),
        'Marine Traffic': L.tileLayer('https://tiles.marinetraffic.com/ais_helpers/shiptilesb/{z}/{x}/{y}.png', {
          attribution: '&copy; MarineTraffic.com'
        }),
        'Depth Contours': L.tileLayer('https://tiles.openseamap.org/depth/{z}/{x}/{y}.png', {
          attribution: '&copy; OpenSeaMap contributors'
        })
      }

      // Add default base layer
      const defaultBaseLayer = store.mapSettings.mapType === 'satellite'
        ? baseLayers.value['Satellite']
        : baseLayers.value['OpenStreetMap']
      defaultBaseLayer.addTo(mapInstance.value)

      // Initialize overlay layers
      overlayLayers.value = {
        'Sea Marks': L.tileLayer('https://tiles.openseamap.org/seamark/{z}/{x}/{y}.png', {
          attribution: 'Map data: &copy; <a href="https://www.openseamap.org/">OpenSeaMap</a> contributors'
        }),
        'Marine Traffic': L.tileLayer('https://tiles.marinetraffic.com/ais_helpers/shiptilesb/{z}/{x}/{y}.png', {
          attribution: '&copy; MarineTraffic.com'
        }),
        'Depth Contours': L.tileLayer('https://tiles.openseamap.org/depth/{z}/{x}/{y}.png', {
          attribution: '&copy; OpenSeaMap contributors'
        })
      }

      // Add layers based on initial settings
      if (store.mapSettings.overlayLayers.showSeaMarks) {
        overlayLayers.value['Sea Marks'].addTo(mapInstance.value)
      }
      if (store.mapSettings.overlayLayers.showMarineTraffic) {
        overlayLayers.value['Marine Traffic'].addTo(mapInstance.value)
      }
      if (store.mapSettings.overlayLayers.showDepthContours) {
        overlayLayers.value['Depth Contours'].addTo(mapInstance.value)
      }

      // Add layer control
      layerControl.value = L.control.layers(baseLayers.value, overlayLayers.value, {
        position: 'topleft',
        collapsed: false
      }).addTo(mapInstance.value)

      // Add geocoder control
      const geocoder = (L.Control as any).geocoder({
        defaultMarkGeocode: false,
        position: 'topleft',
        placeholder: 'Search...',
      }).addTo(mapInstance.value)

      geocoder.on('markgeocode', function (e: any) {
        const bbox = e.geocode.bbox
        mapInstance.value?.fitBounds([
          [bbox.getSouth(), bbox.getWest()],
          [bbox.getNorth(), bbox.getEast()]
        ])
      })

      markerCluster.value = $createMarkerCluster()
      markerCluster.value.addTo(mapInstance.value as L.Map)
    } catch (error) {
      console.error('Error initializing map:', error)
    }
  }
}

// Windy initialization function
const initWindy = () => {
  if (!mapInstance.value) return

  try {
    // Create a container for Windy
    const windyContainer = document.createElement('div')
    windyContainer.id = 'windy'
    windyContainer.style.width = '100%'
    windyContainer.style.height = '100%'
    windyContainer.style.position = 'absolute'
    windyContainer.style.top = '0'
    windyContainer.style.left = '0'
    windyContainer.style.zIndex = '401'
    windyContainer.style.pointerEvents = 'none'
    mapInstance.value.getContainer().appendChild(windyContainer)

    // Create iframe for Windy
    const iframe = document.createElement('iframe')
    iframe.style.width = '100%'
    iframe.style.height = '100%'
    iframe.style.border = 'none'
    iframe.style.position = 'absolute'
    iframe.style.top = '0'
    iframe.style.left = '0'
    iframe.style.pointerEvents = 'none'
    iframe.style.backgroundColor = 'transparent'
    iframe.allowTransparency = true
    iframe.allow = 'geolocation'

    // Set Windy URL with parameters
    const windyURL = new URL('https://embed.windy.com/embed2.html')
    windyURL.searchParams.set('lat', mapInstance.value.getCenter().lat.toString())
    windyURL.searchParams.set('lon', mapInstance.value.getCenter().lng.toString())
    windyURL.searchParams.set('zoom', mapInstance.value.getZoom().toString())
    windyURL.searchParams.set('overlay', store.mapSettings.windySettings.overlay)
    windyURL.searchParams.set('level', store.mapSettings.windySettings.level)
    windyURL.searchParams.set('product', store.mapSettings.windySettings.product)
    windyURL.searchParams.set('menu', 'false')
    windyURL.searchParams.set('message', 'false')
    windyURL.searchParams.set('marker', 'false')
    windyURL.searchParams.set('calendar', 'false')
    windyURL.searchParams.set('pressure', 'false')
    windyURL.searchParams.set('type', 'map')
    windyURL.searchParams.set('location', 'coordinates')
    windyURL.searchParams.set('detail', 'false')
    windyURL.searchParams.set('detailLat', mapInstance.value.getCenter().lat.toString())
    windyURL.searchParams.set('detailLon', mapInstance.value.getCenter().lng.toString())
    windyURL.searchParams.set('metricWind', 'default')
    windyURL.searchParams.set('metricTemp', 'default')
    windyURL.searchParams.set('radarRange', '-1')
    windyURL.searchParams.set('timestamp', store.mapSettings.windySettings.timestamp.toString())
    windyURL.searchParams.set('transparent', '1')
    windyURL.searchParams.set('overlay', store.mapSettings.windySettings.overlay)
    windyURL.searchParams.set('product', store.mapSettings.windySettings.product)
    windyURL.searchParams.set('level', store.mapSettings.windySettings.level)
    windyURL.searchParams.set('lang', 'en')
    windyURL.searchParams.set('hideControls', 'true')
    windyURL.searchParams.set('background', 'transparent')
    windyURL.searchParams.set('textColor', 'transparent')
    windyURL.searchParams.set('hideBottomMenu', 'true')
    windyURL.searchParams.set('hideTopMenu', 'true')

    iframe.src = windyURL.toString()
    windyContainer.appendChild(iframe)
    windyInstance.value = { container: windyContainer, iframe }

    // Set initial visibility
    if (!store.mapSettings.overlayLayers.showWindy) {
      windyContainer.style.display = 'none'
    }

    // Add event listener for map movements
    mapInstance.value.on('moveend', () => {
      if (windyInstance.value?.iframe) {
        const url = new URL(windyInstance.value.iframe.src)
        url.searchParams.set('lat', mapInstance.value!.getCenter().lat.toString())
        url.searchParams.set('lon', mapInstance.value!.getCenter().lng.toString())
        url.searchParams.set('zoom', mapInstance.value!.getZoom().toString())
        windyInstance.value.iframe.src = url.toString()
      }
    })

  } catch (error) {
    console.error('Failed to initialize Windy:', error)
  }
}






// Add Windy watchers
watch(() => store.mapSettings.overlayLayers.showWindy, (show) => {
  if (!windyInstance.value) {
    if (show) {
      initWindy()
    }
    return
  }

  try {
    if (windyInstance.value.container) {
      windyInstance.value.container.style.display = show ? 'block' : 'none'
    }
  } catch (error) {
    console.error('Error toggling Windy overlay:', error)
  }
})

watch(() => store.mapSettings.windySettings, (newSettings) => {
  if (!windyInstance.value?.iframe) return
  try {
    const url = new URL(windyInstance.value.iframe.src)
    url.searchParams.set('overlay', newSettings.overlay)
    url.searchParams.set('level', newSettings.level)
    url.searchParams.set('product', newSettings.product)
    windyInstance.value.iframe.src = url.toString()
  } catch (error) {
    console.error('Error updating Windy settings:', error)
  }
}, { deep: true })



// Add debounced refresh function
const debouncedRefresh = ref<number | null>(null)

const queueRefresh = (skipBoundsFit: boolean = false) => {
  if (debouncedRefresh.value) {
    window.clearTimeout(debouncedRefresh.value)
  }
  debouncedRefresh.value = window.setTimeout(() => {
    refreshMarkers(skipBoundsFit)
    debouncedRefresh.value = null
  }, 250) // Increase to 250ms
}

const zoomIn = () => mapInstance.value.setZoom(mapInstance.value.getZoom() + 1);
const zoomOut = () => mapInstance.value.setZoom(mapInstance.value.getZoom() - 1);

watch(() => response.value, (response) => {
  if (!response) return
  store.updateDevicePosition(response)

  queueRefresh(true)
}, { deep: true, immediate: true })

onMounted(() => {
  if (import.meta.client) {
    initializeMap()
    queueRefresh(false)
    initWindy()
  }


  watch(() => store.positions, () => {
    if (import.meta.client) {
      queueRefresh(true)
    }
  }, { immediate: true, deep: true })

  watch(() => store.visibleDevices, () => {
    queueRefresh(true)
  }, { deep: true })

  watch(() => store.isSidebarOpen, () => {
    setTimeout(() => {
      if (mapInstance.value) {
        mapInstance.value.invalidateSize()
      }
    }, 300)
  })

  watch(() => store.selectedDevice, (newDevice) => {
    if (newDevice) {
      const position = store.getDevicePosition(newDevice)
      if (position && mapInstance.value) {
        mapInstance.value.setView([position.latitude, position.longitude], 16, {
          animate: true,
          duration: 1
        })
      }
    }
  })

  // Add map settings watchers
  watch(() => store.mapSettings.mapType, (newType) => {
    if (!mapInstance.value) return

    // Remove all base layers
    Object.values(baseLayers.value).forEach(layer => {
      mapInstance.value?.removeLayer(layer)
    })

    // Add new base layer based on map type
    const newBaseLayer = newType === 'satellite'
      ? baseLayers.value['Satellite']
      : baseLayers.value['OpenStreetMap']

    newBaseLayer.addTo(mapInstance.value)

  })

  watch(() => store.mapSettings.vesselElement, () => {
    refreshMarkers(true)
  }, { deep: true })

  watch(() => store.mapSettings.vesselStatus, () => {
    refreshMarkers(true)
  }, { deep: true })

  watch(() => store.mapSettings.trailSettings, (newSettings) => {
    deviceTrails.value.forEach((trail) => {
      trail.setStyle({
        weight: newSettings.width,
        opacity: newSettings.opacity
      })
    })
  }, { deep: true })

  // Update the dot trail watcher
  watch(() => store.mapSettings.vesselElement.showDotTrail, (showDotTrail) => {
    deviceTrails.value.forEach((trail) => {
      trail.setStyle({
        dashArray: showDotTrail ? '5, 10' : null
      })
    })
  })

  // Add watchers for overlay layer settings
  watch(() => store.mapSettings.overlayLayers, (newSettings) => {
    if (!mapInstance.value) return

    // Update layer visibility based on settings
    if (overlayLayers.value['Sea Marks']) {
      if (newSettings.showSeaMarks) {
        overlayLayers.value['Sea Marks'].addTo(mapInstance.value)
      } else {
        overlayLayers.value['Sea Marks'].remove()
      }
    }

    if (overlayLayers.value['Marine Traffic']) {
      if (newSettings.showMarineTraffic) {
        overlayLayers.value['Marine Traffic'].addTo(mapInstance.value)
      } else {
        overlayLayers.value['Marine Traffic'].remove()
      }
    }

    if (overlayLayers.value['Depth Contours']) {
      if (newSettings.showDepthContours) {
        overlayLayers.value['Depth Contours'].addTo(mapInstance.value)
      } else {
        overlayLayers.value['Depth Contours'].remove()
      }
    }
  }, { deep: true })
})

// Add CSS for smooth trail transitions
const style = document.createElement('style')
style.textContent = `
	.vessel-trail {
		transition: stroke-width 0.3s ease, opacity 0.3s ease;
	}
	#windy {
		background: transparent !important;
		pointer-events: none;
	}
	#windy iframe {
		background: transparent !important;
		pointer-events: none;
	}
	.leaflet-container {
		background: transparent !important;
	}
	.leaflet-overlay-pane {
		mix-blend-mode: normal;
	}
`
document.head.appendChild(style)

// Clean up style on unmount
onUnmounted(() => {
  style.remove()
  if (debouncedRefresh.value) {
    window.clearTimeout(debouncedRefresh.value)
  }

  // Remove Windy instance and container
  if (windyInstance.value) {
    try {
      const container = document.getElementById('windy')
      if (container) {
        container.remove()
      }
      windyInstance.value = null
    } catch (error) {
      console.error('Error removing Windy instance:', error)
    }
  }

  // Remove Windy script
  const windyScript = document.querySelector('script[src*="api4.windy.com"]')
  if (windyScript) {
    windyScript.remove()
  }

  deviceMarkers.value.forEach((marker) => {
    marker.remove()
  })
  deviceMarkers.value.clear()

  deviceTrails.value.forEach((trail) => {
    trail.remove()
  })
  deviceTrails.value.clear()

  if (mapInstance.value) {
    mapInstance.value.remove()
    mapInstance.value = null
  }
})

</script>

<style>
.leaflet-marker-icon,
.leaflet-marker-shadow,
.leaflet-marker-pane,
.leaflet-overlay-pane,
.leaflet-zoom-animated {
  will-change: transform;
  transform: translate3d(0, 0, 0);
}

.custom-cluster {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  width: 40px !important;
  height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

#map {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  perspective: 1000px;
  -webkit-perspective: 1000px;
}

.leaflet-container {
  width: 100% !important;
  height: 100% !important;
  background: transparent !important;
}

/* Update Windy styles */
.windy-map {
  z-index: 401 !important;
  pointer-events: auto !important;
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent !important;
}

.leaflet-control-container {
  position: absolute;
  z-index: 1000;
  right: 12em;
}

.leaflet-control-layers {
  display: none;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 6px 10px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
}

.leaflet-control-geocoder {
  display: none;
}

.leaflet-control-layers-toggle {
  background-size: 20px 20px;
}

.leaflet-control-geocoder {
  background: white;
  border-radius: 4px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
  margin-top: 10px !important;
}

.custom-popup .leaflet-popup-content-wrapper {
  padding: 0;
  overflow: hidden;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  max-width: 400px;
}

.custom-popup .leaflet-popup-content {
  margin: 0;
  width: auto !important;
}

.custom-popup .leaflet-popup-close-button {
  display: none;
}

.custom-popup .leaflet-popup-tip {
  background: white;
}

.custom-popup .leaflet-popup-content button {
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

.custom-popup .leaflet-popup-content .text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.custom-popup .leaflet-popup-content .text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.custom-popup .leaflet-popup-content .font-medium {
  font-weight: 500;
}

.custom-popup .leaflet-popup-content .font-bold {
  font-weight: 600;
}

.custom-popup .leaflet-popup-content svg {
  flex-shrink: 0;
}
</style>
