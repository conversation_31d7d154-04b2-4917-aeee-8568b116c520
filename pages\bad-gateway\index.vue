<script setup lang="ts">
definePageMeta({
  auth: false,
  layout: false
})

const $router = useRouter()
</script>

<template>
  <div class="min-h-screen flex justify-center items-center p-10">
    <div class="flex flex-col-reverse items-center md:flex-row md:space-x-10">
      <div class="flex flex-col items-center md:items-start">
        <p class="font-semibold text-primary-700 text-center mb-3 md:text-left">502 Bad Gateway</p>
        <p class="text-6xl font-semibold text-gray-900 text-center mb-6 md:text-left">Internal Server Error</p>
        <p class="text-lg text-gray-700 mb-10 text-center md:text-left">It looks like we're having some trouble
          connecting to the server (Error 502:
          Bad Gateway)</p>

        <general-button label="Refresh Page" @on-click="$router.back()" />
      </div>

      <div class="mb-10 md:mb-0">
        <illustration-internal-server-error />
      </div>
    </div>
  </div>
</template>

<style scoped></style>