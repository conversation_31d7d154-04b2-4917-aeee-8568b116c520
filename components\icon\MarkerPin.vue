<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none">
    <path d="M5 14.7864C3.14864 15.6031 2 16.7412 2 18C2 20.4853 6.47715 22.5 12 22.5C17.5228 22.5 22 20.4853 22 18C22 16.7412 20.8514 15.6031 19 14.7864M18 8.5C18 12.5637 13.5 14.5 12 17.5C10.5 14.5 6 12.5637 6 8.5C6 5.18629 8.68629 2.5 12 2.5C15.3137 2.5 18 5.18629 18 8.5ZM13 8.5C13 9.05228 12.5523 9.5 12 9.5C11.4477 9.5 11 9.05228 11 8.5C11 7.94772 11.4477 7.5 12 7.5C12.5523 7.5 13 7.94772 13 8.5Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>