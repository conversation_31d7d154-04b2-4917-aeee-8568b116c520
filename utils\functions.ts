
import {differenceInDays, differenceInHours, differenceInMinutes, formatDuration, set} from "date-fns";

export type AnyObject = null | { [key: string]: any }
export const defaultLatLng = [-2.5, 118]

export function isEmpty(value: any) {
  return !value || value.length === 0 || value === ''
}

export function isArrayHasNull(arr: any[]) {
  return !arr.every(item => item)
}

export function isObjectHasNull(obj: any) {
  return Object.values(obj).includes(null)
}

export function cleanseObject(object: { [key: string]: any }) {
  const validate = (value: any) => {
    return typeof value === 'string' ? value !== '' : value.length > 0
  }

  return Object.fromEntries(
    Object.entries(object).filter(([key, value]) => validate(value))
  )
}

export function isSameString(value: string, key: string) {
  try {
    return value.toLowerCase().includes(key.toLowerCase())
  } catch (_) {
    return false
  }
}

export function isSameArray(arr1: number[] | string[], arr2: number[] | string[]) {
  if (arr1.length !== arr2.length) return false
  return arr1.sort().every((v, i) => v === arr2.sort()[i])
}

export function convertDateToString(date: Date, ignoreTimezone: boolean) {
  if (!date) return ''

  const timezoneOffset = date.getTimezoneOffset()

  const offset = {
    hours: timezoneOffset / 60,
    minutes: timezoneOffset % 60
  }

  const shiftedDate = set(date, {
    hours: date.getHours() - offset.hours,
    minutes: date.getMinutes() - offset.minutes
  })

  return ignoreTimezone ? shiftedDate.toISOString() : date.toISOString()
}

export function constrain(value: number, min: number, max: number) {
  return Math.min(Math.max(value, min), max)
}

export function getCenterMap(coordinates: { lat: number, lng: number }[]): null | { lat: number, lng: number } {
  if (coordinates.length === 0) return null  
  
  const latitudes = coordinates?.map(c => c.lat)
  const longitudes = coordinates?.map(c => c.lng)

  return {
    lat: (Math.min(...latitudes) + Math.max(...latitudes)) / 2,
    lng: (Math.min(...longitudes) + Math.max(...longitudes)) / 2
  }
}

export function getZoomMap(coordinates: { lat: number, lng: number }[]): null | number {
  if (coordinates.length === 0) return null  

  const latitudes = coordinates.map(c => c.lat)
  const longitudes = coordinates.map(c => c.lng)

  const latDiff = Math.max(...latitudes) - Math.min(...latitudes)
  const lngDiff = Math.max(...longitudes) - Math.min(...longitudes)

  const latZoom = Math.ceil(Math.log(360 / latDiff) / Math.LN2)
  const lngZoom = Math.ceil(Math.log(360 / lngDiff) / Math.LN2)

  return Math.min(latZoom, lngZoom)
}

export function capitalizeString(str: string, separator: string | null = null) {
  if (!str) return

  if (!separator) {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
  } else {
    return str.toLowerCase()
      .split(separator)
      .map(s => s.charAt(0).toUpperCase() + s.slice(1))
      .join(' ')
  }
}

export function getDataTableNumber(i: number, page: number, pageSize: number) {
  return (i + 1) + (pageSize * (page - 1))
}

export function getTimeDifference(end: Date, start: Date) {
  const days = differenceInDays(end, start)
  const totalHours = differenceInHours(end, start)
  const hours = totalHours % 24
  const minutes = differenceInMinutes(end, start) % 60

  const dayString = days > 0 ? `${days} Days` : ''
  const hourString = hours > 0 ? ` ${hours} hr` : ''
  const minuteString = minutes > 0 ? ` ${minutes} min` : ''

  return `${dayString}${hourString}${minuteString}`
}


export function convertSecondsToTimeEstimation(seconds: number) {
  const minuteInSeconds = 60
  const hourInSeconds = 60 * minuteInSeconds
  const dayInSeconds = 24 * hourInSeconds
  const monthInSeconds = 30 * dayInSeconds
  const yearInSeconds = 365 * dayInSeconds

  const years = Math.floor(seconds / yearInSeconds)
  const months = Math.floor((seconds % yearInSeconds) / monthInSeconds)
  const days = Math.floor((seconds % monthInSeconds) / dayInSeconds)
  const hours = Math.floor((seconds % dayInSeconds) / hourInSeconds)
  const minutes = Math.floor((seconds % hourInSeconds) / minuteInSeconds)

  return (years === 0 && months === 0 && days === 0 && hours === 0 && minutes === 0)
    ? '0 minutes'
    : formatDuration({years, months, days, hours, minutes})
}

export function downloadBlob(response: Blob, fileName: string) {
  const file = new File([response], fileName, {type: response.type})
  const url = URL.createObjectURL(file)
  const downloadLink = document.createElement('a')
  downloadLink.href = url
  downloadLink.download = fileName
  downloadLink.click()
  URL.revokeObjectURL(url)
}

/**
 * Merges two arrays, `a` and `b`, into a new array `c`.
 * `predicate` is an optional function that determines whether two items are considered equal.
 * If a matching item is found in `a`, it is not added to `c`.
 *
 * @param {Array} a - The first array to be merged.
 * @param {Array} b - The second array to be merged.
 * @param {Function} [predicate=(a, b) => a === b] - The function that determines whether two items are considered equal.
 * @return {Array} - The merged array `c`.
 */
export const merge = (
  a: Array<any>,
  b: Array<any>,
  predicate: Function = (a: any, b: any) => a === b
): Array<any> => {
  const c = [...a]; // copy to avoid side effects
  // add all items from B to copy C if they're not already present
  b.forEach((bItem) =>
    c.some((cItem) => predicate(bItem, cItem)) ? null : c.push(bItem)
  );
  return c;
};

export function getFileName(path: null | string) {
  if (!path) return ''
  const arrPath = path.split('/')
  return arrPath[arrPath.length - 1]
}

export function generateSessionId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}


export const formatCoordinate = (value: number, isLatitude: boolean): string => {
	if (!value) return 'N/A'
	const absolute = Math.abs(value)
	const degrees = Math.floor(absolute)
	const minutes = Math.floor((absolute - degrees) * 60)
	const seconds = Math.round(((absolute - degrees) * 60 - minutes) * 60)

	const direction = isLatitude
		? value >= 0 ? 'N' : 'S'
		: value >= 0 ? 'E' : 'W'

	return `${degrees}° ${minutes}′ ${seconds}″ ${direction}`
}

export const formatAltitude = (altitude: number): string => {
	return altitude ? `${Math.round(altitude)}m` : 'N/A'
}
