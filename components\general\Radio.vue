<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  isChecked: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])
</script>

<template>
  <div class="flex items-center space-x-2">
    <input
      :id="props.id"
      :name="props.name"
      :value="props.value"
      :checked="props.isChecked"
      type="radio"
      class="text-primary-500 focus:ring-transparent cursor-pointer"
      @change="emit('update:modelValue', $event.target.value)"
    >
    <label :for="props.id" class="text-sm text-gray-700 cursor-pointer">{{ props.label }}</label>
  </div>
</template>

<style scoped>

</style>