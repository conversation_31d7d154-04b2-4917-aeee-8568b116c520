// https://nuxt.com/docs/api/configuration/nuxt-config
import pkg from './package.json'

export default defineNuxtConfig({
    compatibilityDate: '2024-11-01',
    devtools: { enabled: true },
    ssr: false,

    modules: [
        '@nuxtjs/tailwindcss',
        '@pinia/nuxt',
        'nuxt3-leaflet',
        '@sidebase/nuxt-auth',
        '@nuxt/image'
    ],

    // Essential build configuration for leaflet
    build: {
        transpile: ['@vuepic/vue-datepicker', 'gsap', 'leaflet.markercluster']
    },

    // Vite optimization for leaflet
    vite: {
        optimizeDeps: {
            include: ['leaflet', 'leaflet.markercluster']
        }
    },

    // Styles
    css: ['@/assets/css/global.css'],
    postcss: {
        plugins: {
            tailwindcss: {},
            autoprefixer: {},
        },
    },

    // Route rules for API proxying
    routeRules: {
        '/':
        {
            ssr: false
        }
        ,
        '/api/auth/signIn':
        {
            proxy: {
                to: `/api/session`
            }
        }
        ,
        '/api/auth/signOut':
        {
            proxy: {
                to: `/api/session`
            }
        }
        ,
        '/api/auth/getSession':
        {
            proxy: {
                to: `/api/session`
            }
        }
    },

    // Auth configuration
    auth: {
        provider: {
            type: 'local',
            endpoints: {
                signIn: { path: `/signIn`, method: 'post' },
                signOut: { path: '/signOut', method: 'delete' },
                getSession: { path: '/getSession', method: 'get' }
            },
            pages: {
                login: '/login'
            },
            token: {
                signInResponseTokenPointer: '/token',
                maxAgeInSeconds: 60 * 60 * 24 // 24 hours
            },
            session: {
                dataType: {
                    email: 'string',
                    id: 'string',
                    integrationTokens: 'any[]',
                    initialProviders: 'string[]'
                }
            }
        },
        globalAppMiddleware: true
    },


    // Runtime configuration
    runtimeConfig: {
        traccarURL: process.env.TRACCAR_URL || 'https://tracking.transtrack.id',
        public: {
            baseAPI: process.env.NUXT_PUBLIC_API_BASE_URL,
            appEnv: process.env.APP_ENV,
            clientVersion: (pkg as any).version, // Type assertion to fix TS error,

            // WebSocket URL
            webSocketURL: process.env.WEB_SOCKET_URL
        }
    },
    nitro: {
        publicAssets: [
            {
                dir: 'workers',
                maxAge: 60 * 60 * 24 * 365 // Cache for 1 year
            }
        ]
    }
})


