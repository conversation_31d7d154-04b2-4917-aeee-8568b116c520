<script setup lang="ts">

import { isEmpty } from '~/utils/functions';
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  data: {
    type: [Number, String],
    default: ''
  },
  isTruncate: {
    type: Boolean,
    default: true
  },
})
</script>

<template>
  <div class="space-y-1">
    <p class="text-sm text-gray-500">{{ props.label }}</p>
    <p class="font-medium text-gray-900" :class="props.isTruncate ? 'truncate' : ''">{{ isEmpty(props.data) ? '-' : props.data }}</p>
  </div>
</template>

<style scoped></style>