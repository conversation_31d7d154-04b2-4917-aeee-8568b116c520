version: '3'
services:
    application:
        image: ${CI_REGISTRY_IMAGE}/application:${CI_COMMIT_TAG}
        restart: unless-stopped
        tty: true
        environment:
            SERVICE_NAME: ${IMAGE_NAME}-${APP_ENV}-application
            SERVICE_TAGS: ${APP_ENV}
        working_dir: /home/<USER>/app
        deploy:
            replicas: ${COMPOSE_REPLICAS:-1}
        networks:
          - proxy
        ports:
          - "${COMPOSE_PUBLIC_PORT}:3000"
        volumes:
          - /etc/localtime:/etc/localtime:ro
          - ./.env:/home/<USER>/app/.env

#Docker Networks
networks:
    proxy:
        external: true