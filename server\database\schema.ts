import { relations } from 'drizzle-orm'
import { pgTable, uuid, text, timestamp, jsonb, integer, boolean, primaryKey } from 'drizzle-orm/pg-core'

export const users = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(),
  email: text('email').notNull().unique(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

export const traccarUserData = pgTable('traccar_user_data', {
  userId: uuid('user_id').notNull().references(() => users.id).primaryKey(),
  traccarId: integer('traccar_id').notNull(),
  name: text('name'),
  administrator: boolean('administrator').default(false),
  readonly: boolean('readonly').default(false),
  deviceLimit: integer('device_limit').default(-1),
  userLimit: integer('user_limit').default(0),
  deviceReadonly: boolean('device_readonly').default(false),
  disabled: boolean('disabled').default(false),
  attributes: jsonb('attributes'),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

export const telematicsUserData = pgTable('telematics_user_data', {
  userId: uuid('user_id').notNull().references(() => users.id).primaryKey(),
  apiHash: text('api_hash').notNull(),
  status: integer('status'),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

export const integrationTokens = pgTable('integration_tokens', {
  userId: uuid('user_id').notNull().references(() => users.id),
  provider: text('provider').notNull(),
  token: text('token').notNull(),
  pid: text('pid').notNull(),
  guid: text('guid').notNull(),
  username: text('username').notNull(),
  expiry: timestamp('expiry').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull()
}, (table) => {
  return {
    pk: primaryKey(table.userId, table.provider)
  }
})

export const usersRelations = relations(users, ({ many }) => ({
  integrationTokens: many(integrationTokens),
}));

export const mdvrUserData = pgTable('mdvr_user_data', {
  userId: uuid('user_id').notNull().references(() => users.id).primaryKey(),
  token: text('token').notNull(),
  pid: text('pid').notNull(),
  guid: text('guid').notNull(),
  username: text('username').notNull(),
  permission: text('permission'),
  roleNumber: text('role_number'),
  updatedAt: timestamp('updated_at').defaultNow().notNull()
})

export const integrationTokensRelations = relations(integrationTokens, ({ one }) => ({
  user: one(users, {
    fields: [integrationTokens.userId],
    references: [users.id],
  }),
}));