<script setup lang="ts">
import VueDatePicker, { type DatePickerInstance } from '@vuepic/vue-datepicker'
import { format, getHours, getMinutes, isAfter, isSameDay } from 'date-fns';

const props = defineProps({
  modelValue: {
    type: Array as () => Date[],
    default: () => ([])
  },
  id: {
    type: String,
    required: true
  },
  required: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  position: {
    type: String,
    default: 'center'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  minDate: {
    type: Date,
    default: undefined
  },
  maxDate: {
    type: Date,
    default: undefined
  },
  timePicker: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:model-value'])

const initialDateTime = ref()
const dateTime = ref()
const selectedDate = ref()
const rangeDatePicker = ref<DatePickerInstance>()

const isSameStartAsMin = computed(() => {
  if (!selectedDate.value || selectedDate.value.length === 0) return false
  return props.minDate && isSameDay(selectedDate.value[0], props.minDate)
})

const isSameEndAsMax = computed(() => {
  if (!selectedDate.value || selectedDate.value.length === 0) return false
  return props.maxDate && isSameDay(selectedDate.value[1], props.maxDate)
})

const isSameStartAsEnd = computed(() => {
  if (!selectedDate.value || selectedDate.value.length < 2) return false
  return isSameDay(selectedDate.value[0], selectedDate.value[1])
})

const minTime = (time: { hours: number[], minutes: number[] }) => {
  const minHour = props.minDate ? getHours(props.minDate) : 0
  const minMinute = props.minDate ? getMinutes(props.minDate) : 0

  const minStart = () => {
    return isSameStartAsMin.value ? {
      hours: minHour,
      minutes: time.hours[0] === minHour ? minMinute : 0
    } : { hours: 0, minutes: 0 }
  }

  const minEnd = () => {
    return isSameStartAsEnd.value ? {
      hours: time.hours[0],
      minutes: time.hours[0] === time.hours[1] ? time.minutes[0] : 0
    } : { hours: 0, minutes: 0 }
  }

  return {
    start: minStart(),
    end: minEnd()
  }
}

const maxTime = (time: { hours: number[], minutes: number[] }) => {
  const maxHour = props.maxDate ? getHours(props.maxDate) : 23
  const maxMinute = props.maxDate ? getMinutes(props.maxDate) : 59

  const maxStart = () => {
    return isSameStartAsEnd.value ? {
      hours: time.hours[1],
      minutes: time.hours[0] === time.hours[1] ? time.minutes[1] : 59
    } : { hours: 23, minutes: 59 }
  }

  const maxEnd = () => {
    return isSameEndAsMax.value ? {
      hours: maxHour,
      minutes: time.hours[1] === maxHour ? maxMinute : 59
    } : { hours: 23, minutes: 59 }
  }

  return {
    start: maxStart(),
    end: maxEnd()
  }
}

const isValid = (dateTime: Date[] | undefined) => {
  if (!dateTime || dateTime.length < 2) {
    return false
  }

  const times = {
    hours: dateTime.map(dt => getHours(dt)),
    minutes: dateTime.map(dt => getMinutes(dt)),
  };

  const min = minTime(times)
  const max = maxTime(times)

  return (
      // Start
      times.hours[0] >= min.start.hours &&
      times.hours[0] <= max.start.hours &&
      times.minutes[0] >= min.start.minutes &&
      times.minutes[0] <= max.start.minutes &&
      // End
      times.hours[1] >= min.end.hours &&
      times.hours[1] <= max.end.hours &&
      times.minutes[1] >= min.end.minutes &&
      times.minutes[1] <= max.end.minutes
  )
}

const placeholderFormat = (date: Date[]) => {
  if (!date || date.length === 0) return ''
  const formatDateTime = `dd/MM/yyyy${props.timePicker ? ', HH:mm' : ''}`
  return `${format(date[0], formatDateTime)} - ${format(date[1], formatDateTime)}`
}

function onOpen() {
  initialDateTime.value = dateTime.value

  if (!dateTime.value || dateTime.value.length < 2) {
    selectedDate.value = [new Date(), new Date()]
    dateTime.value = [...selectedDate.value]
  }
}

function onClosed() {
  dateTime.value = initialDateTime.value
}

function selectDate(value: Date) {
  if (selectedDate.value.length === 0) {
    selectedDate.value.push(value)
    return
  }

  isAfter(new Date(value), selectedDate.value[0])
    ? selectedDate.value.push(value)
    : selectedDate.value.unshift(value)
}

function onSelectRangeStart(value: Date) {
  selectedDate.value = []
  selectDate(value)
}

function onSelectRangeEnd(value: Date) {
  selectDate(value)
}

watch(() => props.modelValue, (value) => {
  dateTime.value = value
}, { immediate: true })
</script>

<template>
  <div class="flex flex-col">
    <label v-if="props.label" :for="id" class="mb-1.5 text-sm font-[600] text-gray-700"
      @click="rangeDatePicker?.openMenu()">
      {{ props.label }}
      <span v-if="props.required" class="text-primary-500">*</span>
    </label>

    <vue-date-picker ref="rangeDatePicker" v-model="dateTime" :id="props.id" :multi-calendars="{ static: false }" :position="props.position"
      time-picker-inline disable-time-range-validation range :format="placeholderFormat" :min-date="props.minDate"
      :max-date="props.maxDate" :placeholder="props.placeholder" :disabled="props.disabled"
      :enable-time-picker="props.timePicker" input-class-name="!py-2 !rounded-lg focus:!ring-primary-500"
      class="custom-date-picker" @range-start="onSelectRangeStart" @range-end="onSelectRangeEnd" @open="onOpen"
      @closed="onClosed" @cleared="selectedDate = undefined; emit('update:model-value', [])">
      <template #time-picker="{ time, updateTime }">
        <div class="flex gap-3 px-3">
          <div class="flex-1 flex items-center justify-between py-3">
            <div class="flex flex-col justify-center space-y-1">
              <p class="text-xs font-medium text-gray-700">Start Time</p>
              <p class="text-xs">{{ format(selectedDate?.[0] || new Date(), 'MMM dd, yyyy') }}</p>
            </div>

            <general-time-input :model-value="{ hours: time.hours[0], minutes: time.minutes[0] }" @update:model-value="($event) => {
              updateTime([$event.hours, time.hours[1]])
              updateTime([$event.minutes, time.minutes[1]], false)
            }" />
          </div>

          <span class="border-l border-gray-300"></span>

          <div class="flex-1 flex items-center justify-between py-3">
            <div class="flex flex-col justify-center space-y-1">
              <p class="text-xs font-medium text-gray-700">End Time</p>
              <p class="text-xs">{{ format(selectedDate?.[1] || new Date(), 'MMM dd, yyyy') }}</p>
            </div>

            <general-time-input :model-value="{ hours: time.hours[1], minutes: time.minutes[1] }" @update:model-value="($event) => {
              updateTime([time.hours[0], $event.hours])
              updateTime([time.minutes[0], $event.minutes], false)
            }" />
          </div>
        </div>
      </template>

      <template #action-row="{ internalModelValue, selectDate }">
        <div class="w-full space-y-3">
          <general-notification color="error" :is-show="!isValid(internalModelValue)" :is-closable="false">
            Invalid selected time
          </general-notification>
          
          <div class="grid grid-cols-2 gap-3 w-full">
            <general-outlined-button label="Cancel" @on-click="rangeDatePicker?.closeMenu()" />
            <general-button label="Confirm" :disabled="!isValid(internalModelValue) || !internalModelValue || (internalModelValue.length < 2)"
              @on-click="() => {
                selectDate()
                emit('update:model-value', internalModelValue)
              }" />
          </div>
        </div>
      </template>
    </vue-date-picker>
  </div>
</template>

<style scoped>
.custom-date-picker :deep(input) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__month_year_row) {
  font-weight: 400;
}

.custom-date-picker :deep(.dp__calendar_header) {
  font-weight: 500 !important;
}

.custom-date-picker :deep(.dp__calendar) {
  font-weight: 400 !important;
}

.custom-date-picker :deep(.dp__theme_light) {
  --dp-text-color: #344054;
  --dp-primary-color: var(--primary-600);
  --dp-range-between-dates-background-color: var(--primary-50);
  --dp-range-between-dates-text-color: var(--primary-700);
}

</style>