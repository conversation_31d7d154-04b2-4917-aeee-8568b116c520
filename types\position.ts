export interface Position {
	id: number;
	deviceId: number;
	protocol: string;
	deviceTime: string;
	fixTime: string;
	serverTime: string;
	outdated: boolean;
	valid: boolean;
	latitude: number;
	longitude: number;
	altitude: number;
	speed: number;
	course: number;
	address: string | null;
	accuracy: number;
	network: string | null;
	attributes: Record<string, any>;
	tail?: string; // Add tail property for vessel trails
}

export interface TelematicsLatestPosition {
	items: Array<{
		id: number;
		lat: number;
		lng: number;
		course: number;
		speed: number;
		altitude: number;
		time: string;
		timestamp: number;
		tail?: string;
	}>;
}