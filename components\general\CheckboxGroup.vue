<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Array as () => boolean[],
    default: () => []
  },
  id: {
    type: String,
    required: true
  },
  label: {
    type: String,
    default: null
  },
  items: {
    type: Array as () => { text: string, value: string }[],
    default: () => []
  },
  returnType: {
    type: String as () => 'boolean' | 'string',
    default: 'boolean'
  }
})

const emit = defineEmits(['update:model-value'])

function onCheck(index: number, value: boolean | string) {
  emit('update:model-value', [...props.modelValue.map((v, i) => i === index ? value : v)])
}

onMounted(() => {
  emit('update:model-value', Array(props.items.length).fill(null))
})
</script>

<template>
  <div class="space-y-3">
    <p v-if="props.label" class="text-sm font-semibold text-gray-700">{{ props.label }}</p>

    <div class="space-y-2">
      <div v-for="(item, i) in items" :key="item.value" class="space-x-2">
        <general-checkbox :id="`${props.id}${i}`" :name="props.id" :value="item.value" :label="item.text"
          :model-value="props.modelValue[i]" :return-type="props.returnType" color="primary"
          @update:model-value="onCheck(i, $event)" />
      </div>
    </div>
  </div>
</template>

<style scoped></style>