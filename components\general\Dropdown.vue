<script setup lang="ts">
import { onMounted, onBeforeUnmount, ref } from "vue";
import { Dropdown, type DropdownInterface } from "flowbite"; // Removed initFlowbite import

const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

const dropdownRef = ref(null)
let dropdown: DropdownInterface | null = null
let isInitialized = ref(false) // Track if dropdown is initialized

const initializeDropdown = () => {
  if (!isInitialized.value) { // Initialize only once
    const $trigger = document.getElementById(`${props.id}-activator`)
    const $target = document.getElementById(`${props.id}`)

    if ($trigger && $target) {
      dropdown = new Dropdown($target, $trigger, {
        placement: 'bottom-end',
        offsetSkidding: 0,
        offsetDistance: 8,
      })

      // Handle scroll events (hide on scroll)
      let ticking = false
      const handleScroll = () => {
        if (!ticking) {
          window.requestAnimationFrame(() => {
            if (dropdown && !dropdown._isHidden) {
              dropdown.hide()
            }
            ticking = false
          })
          ticking = true
        }
      }

      window.addEventListener('scroll', handleScroll, { passive: true })
      window.addEventListener('resize', handleScroll, { passive: true })

      // Handle parent scrollable containers
      const parents = []
      let parent = $trigger.parentElement
      while (parent) {
        const style = window.getComputedStyle(parent)
        if (['auto', 'scroll'].includes(style.overflow) || ['auto', 'scroll'].includes(style.overflowY)) {
          parents.push(parent)
          parent.addEventListener('scroll', handleScroll, { passive: true })
        }
        parent = parent.parentElement
      }

      onBeforeUnmount(() => {
        window.removeEventListener('scroll', handleScroll)
        window.removeEventListener('resize', handleScroll)
        parents.forEach(p => p.removeEventListener('scroll', handleScroll))
      })
      isInitialized.value = true // Mark as initialized
    }
  }
}


onBeforeUnmount(() => {
  if (dropdown) {
    dropdown.hide()
  }
})

const toggle = () => {
  initializeDropdown() // Initialize on first toggle
  if (dropdown) {
    dropdown.toggle()
  }
}
</script>

<template>
  <div class="relative">
    <div :id="`${props.id}-activator`" :data-dropdown-toggle="props.id" data-dropdown-trigger="click">
      <slot name="activator" :toggle="toggle"/>
    </div>

    <Teleport to="body">
      <div :id="`${props.id}`" class="hidden z-[9999]" data-popper-placement="bottom-end" ref="dropdownRef">
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 min-w-[200px]">
          <div :aria-labelledby="`${props.id}-activator`">
            <slot name="content" :close="() => dropdown?.hide()"/>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<style>
[data-popper-placement] {
  position: fixed !important;
}
</style>