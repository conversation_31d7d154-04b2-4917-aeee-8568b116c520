<template>
  <div class="absolute inset-0">
    <!-- Loading overlay -->
    <div
      v-if="isLoading"
      class="absolute inset-0 bg-white/80 z-[1000] flex items-center justify-center"
    >
      <div class="text-center">
        <div
          class="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"
        ></div>
        <p class="text-gray-600">Loading devices...</p>
      </div>
    </div>

    <!-- Map container -->
    <MainMap v-if="!isLoading" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { useMapStore } from "~/store/map";

const { connect, disconnect } = useWebSocket();
const store = useMapStore();
const pollingInterval = ref<number | null>(null);
const isLoading = ref(true);

// Initialize dashboard
onMounted(async () => {
  try {
    connect();

    if (store.devices.length === 0 || store.isReFetching) {
      console.log("Initializing dashboard...")
      await store.initializeDashboard();
    }

    isLoading.value = false;
  } catch (error) {
    console.error("Failed to initialize dashboard:", error);
    navigateTo("/login");
  }
});

// Clean up on unmount
onUnmounted(() => {
  disconnect();
});
</script>
