export const useWebSocket = () => {
  const { data } = useAuth()

  const webSocket = ref<null | WebSocket>(null)
  const isReconnectAble = ref<boolean>(true)

  const response = useState('web-socket:response', () => null)

  const connect = () => {
    if (webSocket.value?.readyState === WebSocket.OPEN) return

    const { webSocketURL } = useRuntimeConfig().public

    webSocket.value = new WebSocket(webSocketURL)

    webSocket.value.onopen = sendMessage
    webSocket.value.onclose = reconnect
    webSocket.value.onmessage = receiveMessage
    webSocket.value.onerror = reconnect
  }

  const disconnect = () => {
    if (!webSocket.value) return

    isReconnectAble.value = false

    webSocket.value.close()
  }

  const reconnect = () => {
    if (!isReconnectAble.value) return

    setTimeout(connect, 1000)
  }

  const sendMessage = () => {
    if (!webSocket.value || !data.value) return

    const message = JSON.stringify({
      telematics_token: (data.value as any).integrationTokens.find(i => i.provider === 'telematics')?.token,
      mdvr_token: (data.value as any).integrationTokens.find(i => i.provider === 'mdvr')?.token
    })

    webSocket.value.send(message)
  }

  const receiveMessage = (event) => {
    response.value = JSON.parse(event.data)
  }

  return {
    response,
    connect,
    disconnect
  }
}