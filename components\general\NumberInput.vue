<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  idInput: {
    type: String,
    default: 'inputQty'
  },
  idMinus: {
    type: String,
    default: 'btnMinus'
  },
  idPlus: {
    type: String,
    default: 'btnPlus'
  },
  label: {
    type: String || null,
    default: null
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  min: {
    type: Number,
    default: 0
  },
  max: {
    type: Number,
    default: 999
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

watch(() => props.modelValue, (value) => {
  if (value <= props.min) { value = props.min }
  else if (value >= props.max) { value = props.max }

  emit('update:modelValue', value)
})
</script>

<template>
  <div class=" flex flex-col items-start">
    <label v-if="props.label" :for="props.idInput" class="mb-1.5 text-sm font-medium text-gray-700">
      {{ props.label }}
      <span v-if="props.required" class="text-primary-500">*</span>
    </label>

    <div class="flex relative">
      <general-icon-button :id="props.idMinus" :disabled="modelValue <= props.min || props.disabled"
        class="rounded-r-none h-[42px] absolute" @on-click="emit('update:modelValue', props.modelValue - 1)">
        <template #icon>
          <icon-minus size="20" />
        </template>
      </general-icon-button>

      <input :disabled="props.disabled" :value="props.modelValue" :id="props.idInput" type="number"
        class="w-32 px-12 border-gray-300 text-center rounded-lg focus:border-gray-300 focus:ring-primary-500 [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
        @input="emit('update:modelValue', Number($event.target.value))">

      <general-icon-button :id="props.idPlus" :disabled="modelValue >= props.max || props.disabled"
        class="absolute right-0 rounded-l-none h-[42px]" @on-click="emit('update:modelValue', props.modelValue + 1)">
        <template #icon>
          <icon-plus size="20" />
        </template>
      </general-icon-button>
    </div>
  </div>
</template>

<style scoped></style>