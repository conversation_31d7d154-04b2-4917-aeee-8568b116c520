<template>
  <div class="border border-t-0 rounded-t-none p-2 rounded-lg bg-white">
    <dynamic-scroller :items="props.items" :min-item-size="props.minItemSize" class="scroller">
      <template #default="{ item, active }">
        <dynamic-scroller-item :item="item" :active="active">
          <div class="py-1">
            <slot name="default" :item="item" />
          </div>
        </dynamic-scroller-item>
      </template>
    </dynamic-scroller>
  </div>
</template>

<script setup lang="ts">
import { DynamicScroller } from 'vue-virtual-scroller';
import { DynamicScrollerItem } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';

const props = defineProps({
  items: {
    type: Array as () => any[],
    default: () => []
  },
  minItemSize: {
    type: Number,
    default: 80
  }
})
</script>

<style scoped>
.scroller {
  max-height: 360px;
  overflow-y: auto;
}
</style>