<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd"
          d="M12.6744 14.3724C12.6744 14.1064 12.8426 13.8719 13.0932 13.7888C13.2592 13.7336 13.4226 13.6654 13.5795 13.5853C13.8149 13.465 14.0983 13.5105 14.2847 13.6986L14.8833 14.3028C14.9171 14.337 14.9743 14.337 15.0081 14.3028L15.8373 13.4659C15.8594 13.4431 15.8629 13.4169 15.8629 13.4029C15.8629 13.3889 15.8594 13.3622 15.8373 13.3399L15.2387 12.7357C15.0523 12.5475 15.0068 12.2614 15.126 12.0238C15.2054 11.8659 15.2734 11.701 15.3276 11.533C15.4095 11.2805 15.6423 11.1108 15.9058 11.1108H16.7515C16.7996 11.1108 16.8399 11.0701 16.8399 11.0215V9.83761C16.8399 9.78949 16.7996 9.74836 16.7515 9.74836H15.9058C15.6423 9.74836 15.4099 9.57861 15.3276 9.32616C15.273 9.1586 15.2054 8.99366 15.126 8.83528C15.0068 8.59771 15.0519 8.31202 15.2387 8.12345L15.8373 7.51925C15.8711 7.48512 15.8711 7.42737 15.8373 7.39325L15.0081 6.55629C14.9743 6.52217 14.9171 6.52217 14.8833 6.55629L14.2847 7.16049C14.0983 7.34862 13.8149 7.39456 13.5795 7.27381C13.423 7.19418 13.2596 7.12549 13.0936 7.07037C12.8431 6.98724 12.6749 6.75273 12.6749 6.48673V5.63315C12.6749 5.58459 12.6346 5.5439 12.5865 5.5439H11.4135C11.3659 5.5439 11.3251 5.58459 11.3251 5.63315V6.48673C11.3251 6.75273 11.1569 6.98724 10.9064 7.07037C10.7404 7.12549 10.577 7.19418 10.4205 7.27381C10.1851 7.39412 9.90167 7.34862 9.71529 7.16049L9.11713 6.55629C9.08332 6.52217 9.02611 6.52217 8.9923 6.55629L8.16311 7.39325C8.14057 7.416 8.13754 7.44225 8.13754 7.45625C8.13754 7.47025 8.141 7.4965 8.16311 7.51925L8.76127 8.12345C8.94765 8.31158 8.99317 8.59771 8.87397 8.83528C8.79508 8.99322 8.72703 9.15816 8.67241 9.32616C8.59006 9.57904 8.35773 9.74836 8.09419 9.74836H7.24853C7.20085 9.74836 7.16011 9.78949 7.16011 9.83761V11.0215C7.16011 11.0701 7.20042 11.1108 7.24853 11.1108H8.09419C8.35773 11.1108 8.59006 11.2805 8.67241 11.533C8.72703 11.7005 8.79465 11.8655 8.87397 12.0238C8.99317 12.2614 8.94809 12.5471 8.76127 12.7357L8.16311 13.3399C8.14057 13.3626 8.13754 13.3889 8.13754 13.4029C8.13754 13.4169 8.141 13.4431 8.16311 13.4659L8.9923 14.3028C9.02611 14.337 9.08332 14.337 9.11713 14.3028L9.71529 13.6986C9.83232 13.5805 9.98793 13.5184 10.1453 13.5184C10.2389 13.5184 10.333 13.5403 10.4205 13.5849C10.577 13.6649 10.7404 13.7336 10.9064 13.7888C11.1569 13.8719 11.3251 14.1064 11.3251 14.3724V15.226C11.3251 15.2741 11.3654 15.3152 11.4135 15.3152H11.7399V16.8487H5.19008C5.04661 16.8487 4.93001 16.9664 4.93001 17.1112V18.8678H3.65091C3.50744 18.8678 3.39084 18.9851 3.39084 19.1303V22.2375C3.39084 22.3823 3.50744 22.5 3.65091 22.5H6.72883C6.8723 22.5 6.9889 22.3823 6.9889 22.2375V19.1307C6.9889 18.9855 6.8723 18.8682 6.72883 18.8682H5.44972V17.3741H11.7399V18.8682H10.4608C10.3174 18.8682 10.2008 18.9855 10.2008 19.1307V22.2375C10.2008 22.3823 10.3174 22.5 10.4608 22.5H13.5387C13.6822 22.5 13.7988 22.3823 13.7988 22.2375V19.1307C13.7988 18.9855 13.6822 18.8682 13.5387 18.8682H12.2601V17.3741H18.5498V18.8682H17.2707C17.1273 18.8682 17.0107 18.9855 17.0107 19.1307V22.2375C17.0107 22.3823 17.1268 22.5 17.2707 22.5H20.3487C20.4926 22.5 20.6087 22.3823 20.6087 22.2375V19.1307C20.6087 18.9855 20.4926 18.8682 20.3487 18.8682H19.07V17.1116C19.07 16.9668 18.9534 16.8491 18.8099 16.8491H12.2601V15.3157H12.5865C12.6341 15.3157 12.6749 15.275 12.6749 15.2264V14.3724H12.6744ZM12 12.6499C10.7872 12.6499 9.80025 11.6541 9.80025 10.43C9.80025 9.20585 10.7868 8.20964 12 8.20964C13.2128 8.20964 14.1993 9.20585 14.1993 10.43C14.1993 11.6537 13.2128 12.6499 12 12.6499ZM15.5903 12.261C15.6813 12.0794 15.7593 11.89 15.8222 11.697C15.8347 11.6581 15.8655 11.6358 15.9062 11.6358H16.7519C17.0874 11.6358 17.3605 11.3601 17.3605 11.0215V9.83761C17.3605 9.49898 17.0874 9.22335 16.7519 9.22335H15.9062C15.8655 9.22335 15.8352 9.20104 15.8222 9.1621C15.7597 8.96959 15.6817 8.77971 15.5903 8.59815C15.5721 8.5614 15.5777 8.52377 15.6067 8.4949L16.2049 7.89113C16.442 7.65138 16.442 7.26199 16.2049 7.02224L15.3757 6.18529C15.1386 5.94597 14.7524 5.94597 14.5149 6.18529L13.9167 6.78905C13.8881 6.81836 13.8504 6.82449 13.8144 6.80567C13.6345 6.7138 13.4464 6.63504 13.2557 6.57161C13.2171 6.55892 13.195 6.52785 13.195 6.48717V5.63359C13.195 5.29495 12.9219 5.01932 12.5865 5.01932H11.4135C11.0781 5.01932 10.805 5.29495 10.805 5.63359V6.48717C10.805 6.52829 10.7829 6.55936 10.7443 6.57204C10.5531 6.63548 10.365 6.71423 10.1856 6.80611C10.1492 6.82449 10.1119 6.8188 10.0833 6.78949L9.4847 6.18572C9.2476 5.94641 8.8614 5.94641 8.62387 6.18572L7.79468 7.02268C7.67981 7.13862 7.61653 7.29262 7.61653 7.45712C7.61653 7.62163 7.67981 7.77563 7.79425 7.89157L8.39284 8.49533C8.42145 8.52421 8.42751 8.56227 8.40931 8.59859C8.31829 8.77971 8.24026 8.96959 8.17741 9.16253C8.16484 9.20147 8.13407 9.22379 8.09332 9.22379H7.24767C6.91218 9.22379 6.6391 9.49942 6.6391 9.83805V11.0219C6.6391 11.3606 6.91218 11.6362 7.24767 11.6362H8.09332C8.13407 11.6362 8.16441 11.6585 8.17741 11.6975C8.23983 11.89 8.31785 12.0798 8.40931 12.2614C8.42795 12.2982 8.42188 12.3358 8.39284 12.3647L7.79468 12.9689C7.73616 13.0275 7.69152 13.0962 7.66161 13.171H5.79995C3.84249 13.171 2.25 11.5636 2.25 9.58779C2.25 7.703 3.70942 6.13235 5.57282 6.01203C5.65257 6.00678 5.72583 5.96478 5.77091 5.89828C5.81598 5.83178 5.82855 5.74778 5.80428 5.67077C5.70676 5.35927 5.65734 5.0342 5.65734 4.70432C5.65734 2.93722 7.08166 1.5 8.83236 1.5C9.99877 1.5 11.069 2.14358 11.6255 3.1796C11.6645 3.25266 11.7352 3.30254 11.8167 3.31435C11.8977 3.32616 11.9796 3.29904 12.0377 3.24041C12.7239 2.55046 13.6345 2.1707 14.6029 2.1707C16.5642 2.1707 18.1944 3.7803 18.2365 5.75871C18.2395 5.89347 18.3431 6.00459 18.4762 6.01466C20.3118 6.15903 21.75 7.72838 21.75 9.58692C21.75 11.5627 20.1575 13.1701 18.1996 13.1701H16.3384C16.308 13.0953 16.2634 13.0266 16.2053 12.968L15.6072 12.3638C15.5781 12.3354 15.5721 12.2977 15.5903 12.261ZM10.3204 10.4296C10.3204 9.4946 11.0737 8.73421 12 8.73421C12.9258 8.73421 13.6796 9.4946 13.6796 10.4296C13.6796 11.3645 12.9258 12.1249 12 12.1249C11.0737 12.1249 10.3204 11.3645 10.3204 10.4296Z"
          fill="#0D0000"/>
  </svg>

</template>

<style scoped>

</style>
