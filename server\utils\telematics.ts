import { TelematicsLatestPosition } from "~/types/position";
import { TelematicsGroup } from "~/types/group";
import { TelematicsDevice } from "~/types/device";
import { TelematicsHistoryResponse } from "~/types/history";

interface TelematicsDeviceGroup {

	id: number;
	title: string;
	items: TelematicsDevice[];
}

export async function loginToTelematics(email: string, password: string): Promise<{status: number, userApiHash: string}> {
	const response = await fetch(
		`https://telematics.transtrack.id/api/login?email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`
	);
	
	const data = await response.json();
	
	if (!data.status || data.status !== 1) {
		throw new Error('Invalid telematics credentials');
	}
	
	return {
		status: data.status,
		userApiHash: data.user_api_hash
	};
}

export async function getDevicesFromTelematics(userApiHash: string): Promise<TelematicsDeviceGroup[]> {
	const response = await fetch(
		`https://telematics.transtrack.id/api/get_devices?lang=en&user_api_hash=${encodeURIComponent(userApiHash)}`
	);
	
	const data = await response.json();
	
	if (!Array.isArray(data)) {
		throw new Error('Invalid response from telematics API');
	}
	
	return data;
}

export async function changeDeviceActive(
	userApiHash: string,
	deviceId: number,
	active: boolean,
	groupId: number = 0,
): Promise<boolean> {
	// Construct base URL with common parameters
	let url = `https://telematics.transtrack.id/api/change_active_device?lang=en&user_api_hash=${encodeURIComponent(userApiHash)}&active=${active}`;
	
	// Add appropriate parameter based on the operation type
	if (groupId >= 0) {
		url += `&group_id=${groupId}`;
	} else {
		url += `&id=${deviceId}`;
	}

	const response = await fetch(
		url,
		{
			method: 'POST',
			headers: {
				'Content-Type': 'application/json'
			}
		}
	);
	
	const data = await response.json();

	if (!data || data.status !== 1) {
		throw new Error('Failed to change device active status');
	}
	
	return true;
}

export async function getLatestPositionsFromTelematics(userApiHash: string): Promise<TelematicsLatestPosition> {
	const response = await fetch(
		`https://telematics.transtrack.id/api/get_devices_latest?lang=en&user_api_hash=${encodeURIComponent(userApiHash)}`
	);
	
	const data = await response.json();

	if (!data.items) {
		throw new Error('Invalid response from telematics API');
	}
	
	return data;
}

export async function getGroupsFromTelematics(userApiHash: string): Promise<TelematicsGroup[]> {
	const response = await fetch(
		`https://telematics.transtrack.id/api/devices_groups?user_api_hash=${encodeURIComponent(userApiHash)}`
	);
	
	const data = await response.json();
	
	if (!data) {
		throw new Error('Invalid response from telematics API');
	}
	
	return Object.entries(data)
		.filter(([key]) => key !== 'pagination')
		.map(([_, value]) => value as TelematicsGroup);
}

export async function getDeviceHistory(
	sessionId: string,
	deviceId: number,
	fromDate: string,
	fromTime: string,
	toDate: string,
	toTime: string,
	snapToRoad: boolean = false
): Promise<TelematicsHistoryResponse> {
	const response = await fetch(
		`https://telematics.transtrack.id/api/get_history?` + 
		`snap_to_road=${snapToRoad}&` +
		`lang=en&` +
		`user_api_hash=${encodeURIComponent(sessionId)}&` +
		`device_id=${deviceId}&` +
		`from_date=${fromDate}&` +
		`from_time=${fromTime}&` +
		`to_date=${toDate}&` +
		`to_time=${toTime}`
	);
	
	const data = await response.json();
	
	if (!data.items) {
		throw new Error('Invalid response from telematics API');
	}
	
	return data;
}
