import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, createError, getQuery } from 'h3'
import { db } from '~/server/database/drizzle'
import { integrationTokens } from '~/server/database/schema'
import { eq, and } from 'drizzle-orm'
import { requireUserSession } from '~/server/utils/session'
import { generateAC } from '~/server/utils/functions'

export default defineEventHandler(async (event) => {
	try {
		const userId = getCookie(event, 'user_id')

		if (!userId) {
			throw createError({
				statusCode: 401,
				statusMessage: 'Unauthorized'
			})
		}

		const query = getQuery(event)

		const fileThumbnail = query.fileThumbnail as string
		const deviceID = query.deviceID as string
		const fileType = query.fileType as string

		if (!fileThumbnail || !deviceID || !fileType) {
			return {
				status: 400,
				msg: 'Missing required query parameters',
				data: null
			}
		}

		// Get the latest valid MDVR token
		const token = await db.query.integrationTokens.findFirst({
			where: and(
				eq(integrationTokens.userId, userId),
				eq(integrationTokens.provider, 'mdvr')
			),
			orderBy: (tokens) => [tokens.createdAt]
		})

		if (!token?.token) {
			return {
				status: 401,
				msg: 'Unauthorized: No valid MDVR token found',
				data: null
			}
		}

		const baseUrl = 'https://mdvr.transtrack.id:36301/fileSrv/showImage.php?';
		const paramsString =
			'token=' + encodeURIComponent(token.token) +
			'&scheme=https' +
			'&fileName=' + fileThumbnail +
			'&deviceID=' + encodeURIComponent(deviceID) +
			'&fileSize=123456' +
			'&fileType=' + encodeURIComponent(fileType) +
			'&ac=cb0a247b8b63924e1290b3d579b22433' +
			'&ipaddr=127.0.0.1';

		const fullUrl = baseUrl + paramsString;

		console.log(fullUrl);

		return {
			status: 10000,
			msg: 'Success',
			data: baseUrl + paramsString.toString()
		}

	} catch (error: any) {
		return {
			status: error.statusCode || 500,
			msg: error.message || 'Failed to generate thumbnail URL',
			data: null
		}
	}
})
