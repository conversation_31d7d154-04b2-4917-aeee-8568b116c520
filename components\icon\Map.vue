<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
    <svg :width="size" :height="size" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M17.9375 4.5625L13.3125 3.5C13.3125 3.40625 13.3438 3.34375 13.3438 3.25C13.3438 1.6875 12.0938 0.4375 10.5625 0.4375C9 0.4375 7.75 1.6875 7.75 3.25C7.75 3.5625 7.84375 3.96875 8.03125 4.4375L7.125 4.625L3.03125 3.46875C2.4375 3.3125 1.8125 3.40625 1.3125 3.78125C0.8125 4.15625 0.53125 4.71875 0.53125 5.34375V14.2813C0.53125 15.1563 1.125 15.9375 1.96875 16.1563L6.90625 17.5625C6.90625 17.5625 6.90625 17.5625 6.9375 17.5625C7 17.5625 7.0625 17.5938 7.125 17.5938C7.1875 17.5938 7.21875 17.5938 7.28125 17.5938L12.9688 16.4063L17.0938 17.375C17.25 17.4063 17.375 17.4375 17.5312 17.4375C17.9688 17.4375 18.4062 17.2813 18.75 17C19.2188 16.625 19.5 16.0625 19.5 15.4688V6.46875C19.4688 5.5625 18.8438 4.78125 17.9375 4.5625ZM10.5312 1.84375C11.3125 1.84375 11.9062 2.46875 11.9062 3.25C11.9062 3.59375 11.4687 4.625 10.5 6.125C9.53125 4.625 9.09375 3.625 9.09375 3.25C9.125 2.46875 9.75 1.84375 10.5312 1.84375ZM8.625 5.71875C8.96875 6.34375 9.375 6.96875 9.75 7.5C9.90625 7.75 10.2187 7.90625 10.5 7.90625C10.8125 7.90625 11.0938 7.75 11.25 7.5C11.5625 7.03125 11.9062 6.53125 12.2187 6V15.0625L7.8125 16V5.90625L8.625 5.71875ZM1.96875 14.25V5.34375C1.96875 5.15625 2.0625 5 2.1875 4.90625C2.3125 4.8125 2.5 4.78125 2.65625 4.8125L6.40625 5.875V15.9375L2.34375 14.7813C2.125 14.7188 1.96875 14.5 1.96875 14.25ZM18.0625 15.4063C18.0625 15.625 17.9375 15.7813 17.8438 15.8438C17.7812 15.9063 17.5938 16 17.375 15.9375L13.625 15.0625V5L17.625 5.9375C17.875 6 18.0625 6.21875 18.0625 6.46875V15.4063Z"/>
      <path d="M10.6562 3.84375C10.6875 3.84375 10.75 3.8125 10.7812 3.8125C10.8125 3.8125 10.875 3.78125 10.9063 3.75C10.9375 3.71875 10.9687 3.6875 11 3.65625C11.125 3.53125 11.2188 3.34375 11.2188 3.15625C11.2188 2.96875 11.1562 2.78125 11 2.65625C10.9687 2.625 10.9375 2.59375 10.9063 2.5625C10.875 2.53125 10.8125 2.5 10.7812 2.5C10.75 2.46875 10.6875 2.46875 10.6562 2.46875C10.4375 2.4375 10.1875 2.5 10.0312 2.65625C9.90625 2.78125 9.8125 2.96875 9.8125 3.15625C9.8125 3.34375 9.875 3.53125 10.0312 3.65625C10.1562 3.78125 10.3438 3.875 10.5313 3.875C10.5625 3.84375 10.625 3.84375 10.6562 3.84375Z"/>
    </svg>
</template>

<style scoped>

</style>
