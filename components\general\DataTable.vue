<script setup lang="ts">
const props = defineProps({
  isLoading: {
    type: Boolean,
    default: false
  },
  headers: {
    type: Array as () => { text: string, value: string }[],
    default: []
  },
  items: {
    type: Array as () => any[],
    default: () => []
  },
  isScrollable: {
    type: Boolean,
    default: false
  },
  emptyTitle: {
    type: String,
    default: 'Empty Data'
  },
  emptySubtitle: {
    type: String,
    default: 'Currently, there are no data in this table. Please add data first.'
  }
})
</script>

<template>
  <div class="w-full bg-white">
    <div class="overflow-x-auto" :class="isScrollable ? 'max-h-screen overflow-y-auto' : ''">
      <table class="w-full text-sm text-left relative table-auto">
        <thead class="bg-gray-50 text-xs text-gray-700 border-b sticky top-0">
        <tr>
          <th
            v-for="header in props.headers"
            :key="header.value"
            scope="col"
            class="px-6 py-3 font-medium text-left select-none"
          >
            <p v-if="!$slots[`header.${header.value}`]">
              {{ header.text }}
            </p>
            <slot :name="`header.${header.value}`" :header="header"/>
          </th>
        </tr>
        </thead>

        <tbody v-if="!props.isLoading && props.items && props.items.length > 0">
        <tr v-for="(item, i) in props.items" :key="i" class="border-b">
          <td
            v-for="header in props.headers"
            :key="header.value"
            class="px-6 py-4 text-gray-900 max-w-[200px] truncate"
          >
            <p v-if="!$slots[`item.${header.value}`]">
              {{ item[header.value] }}
            </p>
            <slot :name="`item.${header.value}`" :item="item" :index="i"/>
          </td>
        </tr>
        </tbody>
      </table>
    </div>

    <div v-if="!props.isLoading && (!props.items || props.items.length === 0)" class="p-10 flex items-center justify-center">
      <div v-if="!$slots.empty_placeholder" class="flex flex-col items-center max-w-sm">
        <div class="bg-primary-50 p-2 rounded-full mb-3">
          <div class="bg-primary-100 p-2 rounded-full">
            <icon-file-search size="24" class="stroke-primary-500"/>
          </div>
        </div>

        <p class="font-semibold text-gray-900 mb-1">{{props.emptyTitle}}</p>
        <p class="text-sm text-gray-500 text-center">{{props.emptySubtitle}}</p>
      </div>

      <slot name="empty_placeholder"/>
    </div>
  </div>
</template>

<style scoped></style>