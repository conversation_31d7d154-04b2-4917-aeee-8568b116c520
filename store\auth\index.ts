import { defineStore } from "pinia"
import { useAuth } from "#imports"
import { toast } from "vue3-toastify"
import { ValidationError } from "yup"
import { useCookie } from "#app"
import { useIsUnauthorized } from "~/composables/is-unauthorized"

export const useAuthStore = defineStore('auth', {
    state: () => ({
        isLoading: false,
        email: '',
        password: '',
        errorMessage: ''
    }),
    actions: {
        async login() {
            this.isLoading = true
            const { signIn } = useAuth()

            const headersOptions = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }

            try {
                const response = await signIn({
                    email: this.email,
                    password: this.password
                }, {
                    callbackUrl: '/dashboard',
                    external: true
                }, {}, headersOptions)

                if (response?.error) {
                    toast.error('Login failed. Please check your credentials.', { toastClassName: 'toastify-error' })
                    throw new Error(response.error)
                }

                useIsUnauthorized().value = false
                return true
            } catch (e: any) {
                if (e.data?.statusCode === 401) {
                    toast.error(e.data?.message, { toastClassName: 'toastify-error' })
                } else {
                    toast.error('An error occurred during login. Please try again.', { toastClassName: 'toastify-error' })
                }
                throw e
            } finally {
                this.isLoading = false
            }
        },

        async logout() {
            try {
                const { signOut } = useAuth()
                this.clearCookies()

                await signOut({ callbackUrl: '/login', external: true })
                window.location.href = '/login'

                return true
            } catch (e) {
                toast.error((e as ValidationError).message)
                throw e
            } finally {
                this.isLoading = false
            }
        },

        clearCookies() {
            const authCookie = useCookie('auth.token');
            const sessionToken = useCookie('session_token');
            const userId = useCookie('user_id');
            const traccarSession = useCookie('traccar.token');
            const telematicsSession = useCookie('telematics.token');
            const mdvrSession = useCookie('mdvr.token');

            if (authCookie.value !== undefined) {
                authCookie.value = null;
            }
            if (userId.value !== undefined) {
                userId.value = null;
            }
            if (sessionToken.value !== undefined) {
                sessionToken.value = null;
            }
            if (traccarSession.value !== undefined) {
                traccarSession.value = null;
            }
            if (telematicsSession.value !== undefined) {
                telematicsSession.value = null;
            }
            if (mdvrSession.value !== undefined) {
                mdvrSession.value = null;
            }
        }
    }
})
