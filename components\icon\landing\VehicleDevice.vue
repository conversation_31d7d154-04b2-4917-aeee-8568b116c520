<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.8673 11.4319L15.9561 12.6019L17.1298 19.0631L20.9998 20.9981L17.9998 11.9981L16.8673 11.4319Z" fill="#0D0000"/>
    <path d="M10.6836 3.17862C7.39111 4.09699 6.03361 8.19612 8.14486 10.9036L11.9999 15.842L15.8549 10.9032C16.5261 10.0407 16.8936 8.98324 16.8936 7.89199C16.8936 4.70449 13.7549 2.31987 10.6836 3.17862ZM11.9999 10.532C10.5599 10.532 9.35611 9.33199 9.35611 7.89199C9.35611 6.45199 10.5599 5.24824 11.9999 5.24824C13.4399 5.24824 14.6436 6.45199 14.6436 7.89199C14.6436 9.33199 13.4399 10.532 11.9999 10.532Z" fill="#0D0000"/>
    <path d="M12.5625 16.948V20.7168L15.975 19.0105L15.0262 13.7905L12.5625 16.948Z" fill="#0D0000"/>
    <path d="M11.4379 16.948L8.97414 13.7905L8.02539 19.0105L11.4379 20.7168V16.948Z" fill="#0D0000"/>
    <path d="M6.87 19.0631L8.04375 12.6019L7.1325 11.4319L6 11.9981L3 20.9981L6.87 19.0631Z" fill="#0D0000"/>
  </svg>
</template>

<style scoped>

</style>
