<script setup lang="ts">

const emit = defineEmits(['mounted', 'negative', 'positive'])
const props = defineProps({
  id: {
    type: String,
    default: 'modal-confirmation'
  },
  isLoading: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  confirmLabel: {
    type: String,
    default: ''
  },
  iconColor: {
    type: String,
    default: 'primary'
  }
})
</script>

<template>
  <general-modal :id="props.id" title="" @mounted="emit('mounted', $event)">
    <template #body>
      <div class="flex flex-col gap-2 items-center">
        <div
          v-if="$slots.icon"
          class="w-12 aspect-square rounded-full bg-primary-100 flex items-center justify-center border-[6px] border-primary-50"
          :class="`bg-${props.iconColor}-100 border-${props.iconColor}-50 stroke-${props.iconColor}-500`"
        >
          <slot name="icon"/>
        </div>

        <div class="space-y-2 mb-5">
          <p class="text-lg text-gray-900 font-[600] text-center">{{ title }}</p>
          <p class="text-sm text-gray-500 font-[400] text-center">{{ subtitle }}</p>
        </div>

        <div class="flex w-full gap-3">
          <general-outlined-button label="Cancel" class="w-full" @on-click="emit('negative')"/>
          <general-button :loading="props.isLoading" :label="confirmLabel" class="w-full" @on-click="emit('positive')"/>
        </div>
      </div>
    </template>
  </general-modal>
</template>

<style scoped>

</style>
