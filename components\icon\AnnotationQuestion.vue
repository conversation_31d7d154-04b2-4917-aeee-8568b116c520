<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 24 25" fill="none">
    <path d="M10 9.00224C10.1762 8.50136 10.524 8.079 10.9817 7.80998C11.4395 7.54095 11.9777 7.4426 12.501 7.53237C13.0243 7.62213 13.499 7.89421 13.8409 8.30041C14.1829 8.70661 14.37 9.22072 14.3692 9.75168C14.3692 11.2506 12.1209 12 12.1209 12M12.1499 15H12.1599M9.9 19.7L11.36 21.6467C11.5771 21.9362 11.6857 22.0809 11.8188 22.1327C11.9353 22.178 12.0647 22.178 12.1812 22.1327C12.3143 22.0809 12.4229 21.9362 12.64 21.6467L14.1 19.7C14.3931 19.3091 14.5397 19.1137 14.7185 18.9645C14.9569 18.7656 15.2383 18.6248 15.5405 18.5535C15.7671 18.5 16.0114 18.5 16.5 18.5C17.8978 18.5 18.5967 18.5 19.1481 18.2716C19.8831 17.9672 20.4672 17.3831 20.7716 16.6481C21 16.0967 21 15.3978 21 14V8.3C21 6.61984 21 5.77976 20.673 5.13803C20.3854 4.57354 19.9265 4.1146 19.362 3.82698C18.7202 3.5 17.8802 3.5 16.2 3.5H7.8C6.11984 3.5 5.27976 3.5 4.63803 3.82698C4.07354 4.1146 3.6146 4.57354 3.32698 5.13803C3 5.77976 3 6.61984 3 8.3V14C3 15.3978 3 16.0967 3.22836 16.6481C3.53284 17.3831 4.11687 17.9672 4.85195 18.2716C5.40326 18.5 6.10218 18.5 7.5 18.5C7.98858 18.5 8.23287 18.5 8.45951 18.5535C8.76169 18.6248 9.04312 18.7656 9.2815 18.9645C9.46028 19.1137 9.60685 19.3091 9.9 19.7Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
</template>

<style scoped>

</style>