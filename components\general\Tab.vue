<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  tabs: {
    type: Array as () => {text: string, value: string}[],
    default: []
  }
})

const emit = defineEmits(['update:model-value'])

const tabClass = (i: number) => {
  const baseClass = 'py-2 px-3 border-b-2 text-gray-500 transition'
  const color = i === props.modelValue ? 'bg-primary-50 text-primary-500 border-primary-500' : 'border-transparent hover:bg-gray-100 hover:border-gray-300'
  return `${baseClass} ${color}`
}
</script>

<template>
  <div>
    <div class="mb-6 flex items-center justify-between">
      <ul class="w-fit flex flex-wrap font-semibold">
        <li v-for="(tab, i) in props.tabs" class="me-3">
          <button
            type="button"
            :class="tabClass(i)"
            @click="emit('update:model-value', i)"
          >
            {{ tab.text }}
          </button>
        </li>
      </ul>

      <slot name="action-button"/>
    </div>

    <slot :name="`tab-item.${props.tabs[props.modelValue].value}`"/>
  </div>
</template>

<style scoped>

</style>