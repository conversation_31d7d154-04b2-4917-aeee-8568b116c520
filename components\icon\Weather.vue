<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '20'
  }
})
</script>

<template>
  <svg :width="size" :height="size" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_604_70989)">
      <path d="M8.75033 1.25V2.58333M3.00033 8.33333H1.66699M4.54299 4.12614L3.60018 3.18333M12.9585 4.12614L13.9013 3.18333M15.8337 8.33333H14.5003M5.41705 8.3334C5.41705 6.49245 6.90944 5.00006 8.75039 5.00006C10.0054 5.00006 11.0984 5.69363 11.6671 6.71838M5.00033 18.3333C3.15938 18.3333 1.66699 16.8409 1.66699 15C1.66699 13.1591 3.15938 11.6667 5.00033 11.6667C5.38715 11.6667 5.75858 11.7326 6.10401 11.8537C6.70083 10.283 8.22022 9.16667 10.0003 9.16667C11.7804 9.16667 13.2998 10.283 13.8966 11.8537C14.2421 11.7326 14.6135 11.6667 15.0003 11.6667C16.8413 11.6667 18.3337 13.1591 18.3337 15C18.3337 16.8409 16.8413 18.3333 15.0003 18.3333C11.1334 18.3333 8.233 18.3333 5.00033 18.3333Z" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </g>
    <defs>
      <clipPath id="clip0_604_70989">
        <rect :width="size" :height="size"/>
      </clipPath>
    </defs>
  </svg>
</template>

<style scoped>

</style>
