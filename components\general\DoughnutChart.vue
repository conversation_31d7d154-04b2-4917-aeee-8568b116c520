<script setup lang="ts">
import { <PERSON><PERSON>ut as <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'vue-chartjs'
import {
  Chart as ChartJS,
  ArcElement,
  <PERSON><PERSON><PERSON>,
  Legend
} from 'chart.js'

ChartJS.register(
  ArcElement,
  Tooltip,
  Legend
)

const props = defineProps({
  labels: {
    type: Array as () => string[],
    default: []
  },
  datasets: {
    type: Array as () => {colors: string[], data: any[]}[],
    default: []
  }
})

const chartOptions = ref({
  responsive: true,
  maintainAspectRatio: false,
  cutout: 42,
  plugins: {
    legend: {
      position: 'right',
      labels: {
        font: {size: 14},
        color: '#667085',
        boxHeight: 8,
        usePointStyle: true
      }
    }
  }
})

const chartData = computed(() => {
  return {
    labels: props.labels,
    datasets: props.datasets.map(dataset => ({
      backgroundColor: [...dataset.colors],
      data: [...dataset.data]
    }))
  }
})
</script>

<template>
  <doughnut-chart
    :data="chartData"
    :options="chartOptions"
  />
</template>