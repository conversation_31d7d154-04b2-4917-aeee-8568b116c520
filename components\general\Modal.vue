<template>
  <slot name="trigger" :activator="() => { toggleModal(true); emit('trigger-click') }"/>
  <div :id=props.id aria-hidden="true"
       class="backdrop-blur-sm fixed flex justify-center items-center align-center z-50 hidden bg-black bg-opacity-50 md:p-5 overflow-x-hidden inset-0 h-full">
    <div :class="`relative w-full ${classModal} max-h-full overflow-y-auto bg-white rounded-lg shadow dark:bg-gray-700`">
      <div class="relative p-4 max-h-full">
        <div v-if="$slots.icon || !isEmpty(props.title) || !isEmpty(props.subtitle)" class="w-full p-2">
          <div class="flex items-start justify-between">
            <div class="flex flex-col items-start space-y-4">
              <div v-if="$slots.icon" class="w-12 aspect-square rounded-full bg-primary-100 flex items-center justify-center border-[6px] border-primary-50 stroke-primary-500">
                <slot name="icon"/>
              </div>

              <div class="space-y-2">
                <h3 v-if="props.title" class="text-lg font-semibold text-gray-900 dark:text-gray-300">
                  {{ props.title }}
                </h3>
                <h5 v-if="props.subtitle" class="text-sm text-gray-500 dark:text-gray-300">
                  {{ props.subtitle }}
                </h5>
              </div>
            </div>

            <button v-if="props.isHasClose" type="button"
                    class="top-9 right-8 text-gray-400 bg-transparent transition hover:bg-gray-200 hover:text-gray-900 rounded-full text-sm p-1 ml-auto inline-flex items-center dark:hover:bg-gray-800 dark:hover:text-white"
                    @click.prevent="toggleModal(false)">
              <icon-close size="20" class="stroke-gray-700"/>
              <span class="sr-only">Close modal</span>
            </button>
          </div>
        </div>

        <div class="w-full p-2">
          <slot name="body"/>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">


import {useModal} from "~/composables/modal";
import type {ElementEvent} from "~/types/element";
import { isEmpty } from "~/utils/functions";

const emit = defineEmits(['mounted', 'modal-opened', 'modal-closed', 'trigger-click'])
const props = defineProps({
  id: {
    type: String,
    required: true
  },
  title: {
    type: String,
  },
  subtitle: {
    type: String,
  },
  isHasClose: {
    type: Boolean,
    default: false
  },
  classModal: {
    type: String,
    default: 'max-w-lg'
  }
})
const isVisible = ref<boolean>(false)

let $modal: HTMLElement | null = null

onMounted(() => {
  $modal = document.getElementById(props.id)

  emit('mounted', setModal)
})

const setModal: ElementEvent = {
  show: () => toggleModal(true),
  hide: () => toggleModal(false),
  toggle: () => toggleModal(!isVisible.value)
}

const toggleModal = (value: boolean) => {
  if (value) {
    $modal?.classList.add('animate-fade-in')
    $modal?.classList.remove('hidden')
    $modal?.classList.remove('animate-fade-out')
    emit('modal-opened')
  } else {
    $modal?.classList.remove('animate-fade-in')
    $modal?.classList.add('animate-fade-out')
    setTimeout(() => {
        $modal?.classList.add('hidden')
        emit('modal-closed')
      }, 300
    )
  }

  isVisible.value = value
  useModal().value = !useModal().value
}


</script>

<style scoped>

</style>
