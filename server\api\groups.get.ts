import { defineEvent<PERSON><PERSON><PERSON>, getCookie } from 'h3';
import { getGroupsFromTraccar } from '../utils/traccar';
import { getGroupsFromTelematics } from '../utils/telematics';
import { UnifiedGroup, TelematicsGroup } from '~/types/group';

function convertTelematicsToUnified(group: TelematicsGroup): UnifiedGroup {

	return {
		id: group.id,
		name: group.title,
		source: 'telematics',
		attributes: {}
	};
}

function convertTraccarToUnified(group: any): UnifiedGroup {
	return {
		...group,
		source: 'traccar'
	};
}

export default defineEventHandler(async (event) => {
	const traccarSessionId = getCookie(event, 'traccar.token');
	const telematicsSessionId = getCookie(event, 'telematics.token');

	const groups: UnifiedGroup[] = [];

	if (traccarSessionId) {
		try {
			const traccarGroups = await getGroupsFromTraccar(traccarSessionId);
			groups.push(...traccarGroups.map(convertTraccarToUnified));
		} catch (error) {
			console.error('Failed to fetch Traccar groups:', error);
		}
	}

	if (telematicsSessionId) {
		try {
			const telematicsGroups = await getGroupsFromTelematics(telematicsSessionId);
			groups.push(...telematicsGroups.map(convertTelematicsToUnified));
		} catch (error) {
			console.error('Failed to fetch Telematics groups:', error);
		}
	}

	return groups;
});