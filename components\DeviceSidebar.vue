<template>
  <div class="flex h-screen w-full overflow-hidden">
    <!-- Main sidebar -->
    <div class="flex-none bg-white shadow-lg z-[100] transition-all duration-300 ease-in-out"
      :style="{ width: store.isSidebarOpen ? `${sidebarWidth}px` : '64px' }">
      <div class="h-full flex flex-col">

        <!-- Logo -->
        <div class="flex justify-center items-center h-14 px-4 border-b border-gray-200">
          <transition name="fade" mode="out-in">
            <IconLogoTransTRACK />
          </transition>
        </div>

        <!-- Toggle button -->
        <div class="flex-none px-3 py-3 border-b border-gray-200">
          <button @click="toggleSidebar"
            class="w-full p-2 flex items-center justify-center rounded-lg hover:bg-gray-100 transition-colors">
            <lazy-icon-arrow-left class="stroke-gray-900 w-5 h-5 transition-transform"
              :class="[store.isSidebarOpen ? '' : 'rotate-180']" />
          </button>
        </div>


        <!-- Navigation -->
        <div class="flex-1 py-4 overflow-y-auto">
          <ul class="px-3 space-y-1">
            <li v-for="link in links" :key="link.id">
              <general-navigation-link :id="link.id" :name="link.name" :destination="link.destination"
                :is-show-name="store.isSidebarOpen">
                <template #icon>
                  <component :is="link.icon" class="w-5 h-5 transition-colors" :class="[
                    routePath.startsWith(link.destination)
                      ? 'stroke-blue-600'
                      : 'stroke-gray-500 group-hover:stroke-gray-900'
                  ]" />
                </template>
              </general-navigation-link>
            </li>
          </ul>
        </div>

        <!-- Profile section -->
        <div class="flex items-center justify-center px-3 py-3 border-t border-gray-200">
          <general-dropdown id="dropdown-profile" :offset="[0, 8]">
            <template #activator>
              <div
                class="flex items-center justify-between cursor-pointer p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="flex items-center gap-3">
                  <div class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                    <icon-user class="stroke-gray-900 w-5 h-5" />
                  </div>
                  <div v-if="store.isSidebarOpen" class="flex flex-col">
                    <span class="text-sm font-medium text-gray-900">{{ data?.name ?? 'Admin' }}</span>
                  </div>
                </div>
                <icon-arrow-down v-if="store.isSidebarOpen" class="fill-gray-900 w-4 h-4" />
              </div>
            </template>
            <template #content>
              <div class="w-[200px] py-1 bg-white rounded-lg shadow-lg border border-gray-200">
                <button @click="$auth.logout()"
                  class="w-full px-4 py-2 flex items-center gap-2 text-red-500 hover:bg-gray-50 transition-colors">
                  <icon-logout class="stroke-current" />
                  <span class="text-sm">Logout</span>
                </button>
              </div>
            </template>
          </general-dropdown>
        </div>
      </div>
    </div>


    <!-- Content wrapper -->
    <div class="flex-1 relative">
      <!-- Main content -->
      <div class="absolute inset-0">
        <slot name="default" />
      </div>

      <!-- Asset List Floating Container -->
      <div v-show="routePath.startsWith('/dashboard')" class="absolute top-4 left-4 z-[95] h-0">
        <!-- Header - Always visible -->
        <div class="bg-white rounded-lg shadow-lg w-[500px] mb-2">
          <div class="flex items-center justify-between p-4 cursor-pointer" @click="store.toggleMenuSidebar">
            <div class="flex items-center gap-2">
              <h2 class="text-lg font-semibold">Asset List</h2>
              <span class="text-sm text-gray-500">({{ store.devices.length }})</span>
            </div>
            <icon-chevron-up class="w-5 h-5 stroke-blue-600 transform transition-transform duration-300"
              :class="[store.isSidebarMenuOpen ? 'rotate-180' : '']" />
          </div>
        </div>

        <!-- Content - Collapsible -->
        <div
          class="bg-white rounded-lg shadow-lg w-[500px] max-h-[calc(100vh-6rem)] overflow-auto transition-all duration-300 origin-top"
          :class="[store.isSidebarMenuOpen ? 'transform-none opacity-100' : 'transform scale-y-0 opacity-0 -translate-y-4']"
          :style="{ pointerEvents: store.isSidebarMenuOpen ? 'auto' : 'none', zIndex: store.isSidebarMenuOpen ? 95 : -1 }">
          <div class="p-4 space-y-4">
            <!-- Search and filter -->
            <div class="space-y-4">
              <general-text-input v-model="store.searchQuery" placeholder="Search device in here..." :clearable="true">
                <template #prefix>
                  <icon-search size="20" />
                </template>
                <template #suffix>
                  <button class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    @click="showFilterDropdown = !showFilterDropdown">
                    <icon-filter-lines size="20"
                      :class="store.hasActiveFilters ? 'text-primary-500' : 'text-gray-400'" />
                  </button>
                </template>
              </general-text-input>
              <!-- Add filter dropdown content -->
              <div v-if="showFilterDropdown"
                class="absolute right-0 top-full mt-2 w-72 bg-white rounded-lg shadow-xl border border-gray-200 z-[1000]">
                <div class="p-4 space-y-4">
                  <!-- Filter header -->
                  <div class="flex justify-between items-center pb-2 border-b">
                    <div class="flex items-center gap-2">
                      <h3 class="font-medium text-gray-900">Filter & Sort</h3>
                      <button @click="store.resetFilters"
                        class="text-xs text-primary-500 hover:text-primary-600 transition-colors">
                        Reset
                      </button>
                    </div>
                    <button @click="showFilterDropdown = false"
                      class="p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors">
                      <icon-close class="w-4 h-4" />
                    </button>
                  </div>

                  <!-- Sort options -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Sort By</label>
                    <select v-model="store.sortBy"
                      class="w-full rounded-md border border-gray-300 py-1.5 px-2 text-sm focus:border-primary-500 focus:outline-none">
                      <option value="name">Name (A-Z)</option>
                      <option value="name-desc">Name (Z-A)</option>
                      <option value="status">Status</option>
                      <option value="last-updated">Last Updated</option>
                    </select>
                  </div>

                  <!-- Status filter -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Status Filter</label>
                    <div class="flex flex-wrap gap-1.5">
                      <button v-for="status in ['online', 'offline', 'ack', 'idle']" :key="status" @click="() => {
                        const index = store.filters.status.indexOf(status);
                        if (index === -1) {
                          store.filters.status.push(status);
                        } else {
                          store.filters.status = store.filters.status.filter(s => s !== status);
                        }
                      }" class="flex items-center space-x-1.5 px-2 py-1 rounded-full text-xs border" :class="[
                        store.filters.status.includes(status)
                          ? 'border-primary-500 bg-primary-50 text-primary-700'
                          : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50'
                      ]">
                        <div class="w-1.5 h-1.5 rounded-full" :class="statusConfig[status].color"></div>
                        <span>{{ statusConfig[status].label }}</span>
                      </button>
                    </div>
                  </div>

                  <!-- Group filter -->
                  <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700">Group Filter</label>
                    <div class="flex flex-col space-y-1">
                      <button @click="() => {
                        const index = store.filters.groups.indexOf('ungrouped');
                        if (index === -1) {
                          store.filters.groups.push('ungrouped');
                        } else {
                          store.filters.groups = store.filters.groups.filter(id => id !== 'ungrouped');
                        }
                      }" class="flex items-center px-2 py-1 rounded text-xs text-left" :class="[
                        store.filters.groups.includes('ungrouped')
                          ? 'bg-primary-50 text-primary-700'
                          : 'hover:bg-gray-50 text-gray-700'
                      ]">
                        Ungrouped
                      </button>
                      <button v-for="group in groupOptions" :key="group.id" @click="() => {
                        const index = store.filters.groups.indexOf(group.id);
                        if (index === -1) {
                          store.filters.groups.push(group.id);
                        } else {
                          store.filters.groups = store.filters.groups.filter(id => id !== group.id);
                        }
                      }" class="flex items-center px-2 py-1 rounded text-xs text-left" :class="[
                        store.filters.groups.includes(group.id)
                          ? 'bg-primary-50 text-primary-700'
                          : 'hover:bg-gray-50 text-gray-700'
                      ]">
                        {{ group.name }}
                      </button>
                    </div>
                  </div>

                  <div class="flex justify-end pt-2 border-t">
                    <Button variant="primary" label="Close" @click="() => {
                      showFilterDropdown = false;
                    }" />
                  </div>
                </div>
              </div>


              <!-- Stats -->
              <div class="flex flex-wrap items-center gap-3 text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                <div class="flex items-center gap-2">
                  <icon-device class="w-4 h-4" />
                  <span class="font-medium">{{ store.devices.length }} Total</span>
                </div>
                <div class="w-px h-4 bg-gray-300"></div>
                <div class="flex items-center gap-2">
                  <icon-folder class="w-4 h-4" />
                  <span>{{ groupedDevicesCount + store.mdvrDevices.length }} In Groups</span>
                </div>
                <div class="w-px h-4 bg-gray-300"></div>
                <div class="flex items-center gap-2">
                  <icon-device class="w-4 h-4" />
                  <span>{{ ungroupedDevicesCount }} Ungrouped</span>
                </div>
              </div>

              <!-- Actions -->
              <!-- <div class="flex items-center gap-2"> -->
              <!-- <button class="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                  <icon-checkbox class="w-4 h-4" />
                  <span>Mark All Asset</span>
                </button> -->
              <!-- <button class="flex items-center gap-2 px-3 py-1.5 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                  <icon-plus class="w-4 h-4" />
                  <span>Add New Group</span>
                </button> -->
              <!-- </div> -->
            </div>

            <!-- Panel content -->
            <div class="h-full flex flex-col">
              <!-- Tabs -->
              <!--              <div class="flex-none border-b">-->
              <!--                <div class="flex">-->
              <!--                  <button v-for="tab in ['devices', 'settings']" :key="tab"-->
              <!--                          class="flex-1 px-4 py-3 text-sm font-medium transition-colors" :class="[-->
              <!--                      activeTab === tab-->
              <!--                        ? 'text-primary-600 border-b-2 border-primary-500'-->
              <!--                        : 'text-gray-500 hover:text-gray-700'-->
              <!--                    ]" @click="activeTab = tab">-->
              <!--                    {{ tab.charAt(0).toUpperCase() + tab.slice(1) }}-->
              <!--                  </button>-->
              <!--                </div>-->
              <!--              </div>-->

              <!-- Tab content -->
              <div class="flex-1 overflow-y-auto">
                <transition name="fade" mode="out-in">
                  <div v-if="activeTab === 'devices'" key="devices" class="space-y-4">
                    <!-- Device groups -->
                    <div class="space-y-2">
                      <general-collapse v-if="store.mdvrDevices.length > 0" id="mdvr-devices"
                        class="w-full rounded-lg bg-gray-50"
                        trigger-class="p-2 hover:bg-gray-50 rounded-lg transition-colors" is-horizontal-icon>
                        <template #label>
                          <div class="flex items-center gap-2 w-full">
                            <span>MDVR Devices</span>
                            <span class="text-sm text-blue-600">({{
                              store.mdvrDevices.length
                            }})</span>
                          </div>
                        </template>

                        <template #icon>
                          <div class="flex items-center gap-2">
                            <general-checkbox :model-value="store.isMdvrVisible" @update:model-value="(value) => {
                              store.toggleGroupVisibility(undefined, value);
                            }" @click.stop color="blue" />
                          </div>
                        </template>

                        <template #content>
                          <general-dynamic-virtual-scroller :items="store.mdvrDevices">
                            <template #default="{ item }">
                              <DeviceItem :device="item" :selected="store.selectedDevice?.id === item.id"
                                :visible="store.visibleDevices.has(item.id)"
                                :following="store.isFollowingDevice && store.selectedDevice?.id === item.id"
                                :position="store.getDevicePosition(item)"
                                class="hover:bg-gray-50 rounded-lg transition-colors" @click="store.selectDevice(item)"
                                @toggleVisibility="store.toggleDeviceVisibility(item.id)"
                                @toggleFollow="store.toggleFollowDevice(item)" />
                            </template>
                          </general-dynamic-virtual-scroller>
                        </template>
                      </general-collapse>

                      <general-collapse v-if="store.ungroupedDevices.length > 0" :id="'ungrouped'"
                        class="w-full rounded-lg bg-gray-50"
                        trigger-class="p-2 hover:bg-gray-50 rounded-lg transition-colors" is-horizontal-icon>
                        <template #label>
                          <div class="flex items-center gap-2 w-full">
                            <span>Ungrouped Devices</span>
                            <span class="text-sm text-blue-600">({{ store.ungroupedDevices.length }})</span>
                          </div>
                        </template>

                        <template #icon>
                          <div class="flex items-center gap-2">
                            <general-checkbox :model-value="store.isUngroupedVisible" @update:model-value="(value) => {
                              store.toggleGroupVisibility(0, value);
                            }" @click.stop color="blue" />
                          </div>
                        </template>

                        <template #content>
                          <general-dynamic-virtual-scroller :items="store.ungroupedDevices">
                            <template #default="{ item }">
                              <DeviceItem :device="item" :selected="store.selectedDevice?.id === item.id"
                                :visible="store.visibleDevices.has(item.id)"
                                :following="store.isFollowingDevice && store.selectedDevice?.id === item.id"
                                :position="store.getDevicePosition(item)"
                                class="hover:bg-gray-50 rounded-lg transition-colors" @click="store.selectDevice(item)"
                                @toggleVisibility="store.toggleDeviceVisibility(item.id)"
                                @toggleFollow="store.toggleFollowDevice(item)" />
                            </template>
                          </general-dynamic-virtual-scroller>
                        </template>
                      </general-collapse>

                      <general-collapse v-for="group in store.filteredGroups" :key="group.id" :id="'group-' + group.id"
                        class="w-full rounded-lg bg-gray-50"
                        trigger-class="p-2 hover:bg-gray-50 rounded-lg transition-colors" is-horizontal-icon>
                        <template #label>
                          <div class="flex items-center gap-2 w-full">
                            <span>{{ group.name }}</span>
                            <span class="text-sm text-blue-600">({{
                              store.getDevicesInGroup(group.id).length
                            }})</span>
                          </div>
                        </template>

                        <template #icon>
                          <div class="flex items-center gap-2">
                            <general-checkbox :model-value="store.isGroupVisible(group.id)" @update:model-value="(value) => {
                              store.toggleGroupVisibility(group.id, value);
                            }" @click.stop color="blue" />
                          </div>
                        </template>

                        <template #content>
                          <general-dynamic-virtual-scroller :items="store.getDevicesInGroup(group.id)">
                            <template #default="{ item }">
                              <DeviceItem :device="item" :selected="store.selectedDevice?.id === item.id"
                                :visible="store.visibleDevices.has(item.id)"
                                :following="store.isFollowingDevice && store.selectedDevice?.id === item.id"
                                :position="store.getDevicePosition(item)"
                                class="hover:bg-gray-50 rounded-lg transition-colors" @click="store.selectDevice(item)"
                                @toggleVisibility="store.toggleDeviceVisibility(item.id)"
                                @toggleFollow="store.toggleFollowDevice(item)" />
                            </template>
                          </general-dynamic-virtual-scroller>
                        </template>
                      </general-collapse>
                    </div>
                  </div>
                  <!--                  <div v-else key="settings">-->
                  <!--                    <MapSettings/>-->
                  <!--                  </div>-->
                </transition>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-show="routePath.startsWith('/dashboard')" class="absolute top-4 right-4 z-[95]">
        <div class="border-transparent rounded-sm bg-gray-400 bg-opacity-50 p-2 space-y-2">
          <general-icon-button :bordered="false" color=""
            :class="`${menuMapType ? 'bg-blue-600' : 'bg-white'} w-[36px] h-[36px]`" @on-click="toggleMenuMapType()">
            <template #icon>
              <icon-map :class="`${menuMapType ? 'fill-white' : 'fill-blue-500'}`" />
            </template>
          </general-icon-button>
          <general-icon-button :bordered="false" color=""
            :class="`${menuMapSettings ? 'bg-blue-600' : 'bg-white'} w-[36px] h-[36px]`"
            @on-click="toggleMenuMapSettings()">
            <template #icon>
              <icon-weather :class="`${menuMapSettings ? 'stroke-white' : 'stroke-blue-500'}`" />
            </template>
          </general-icon-button>
        </div>
      </div>

      <div v-if="menuMapType"
        class="absolute transition-all animate-fade-in  border top-4 right-20 rounded-lg bg-white p-5 z-[100]">
        <div class="flex justify-between">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Map Type</h3>
          <icon-close class="stroke-black cursor-pointer" @click="menuMapType = false" />
        </div>
        <div class="grid lg:grid-cols-2 grid-cols-1 gap-3">
          <button class="p-3 border rounded-lg flex flex-col items-center gap-2"
            :class="[store.mapSettings.mapType === 'satellite' ? 'border-blue-600 bg-blue-50' : 'border-gray-200']"
            @click="store.setMapType('satellite')">
            <illustration-satelite class="border rounded-lg" />
            <span class="text-sm">Satellite</span>
          </button>
          <button class="p-3 border rounded-lg flex flex-col items-center gap-2"
            :class="[store.mapSettings.mapType === 'vector' ? 'border-blue-600 bg-blue-50' : 'border-gray-200']"
            @click="store.setMapType('vector')">
            <illustration-vector class="border rounded-lg" />
            <span class="text-sm">Vector</span>
          </button>
        </div>
      </div>

      <div v-if="menuMapSettings"
        class="absolute transition-all animate-fade-in  border top-4 right-20 rounded-lg bg-white p-5 z-[100]">
        <div class="flex justify-between">
          <h3 class="text-sm font-medium text-gray-700 mb-3">Map Settings</h3>
          <icon-close class="stroke-black cursor-pointer" @click="menuMapSettings = false" />
        </div>
        <MapSettings />
      </div>

    </div>
  </div>
</template>


<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { useMapStore } from '~/store/map'
import { useAuthStore } from '~/store/auth'
import iconBarChart from '~/components/icon/BarChart.vue'
import iconDevice from '~/components/icon/Building.vue'
import iconSettings from '~/components/icon/Settings.vue'
import iconSearch from '~/components/icon/Search.vue'
import iconFilterLines from '~/components/icon/FilterLines.vue'
import iconClose from '~/components/icon/Close.vue'
import iconChevronUp from '~/components/icon/ChevronUp.vue'
import iconArrowLeft from '~/components/icon/ArrowLeft.vue'
import iconArrowDown from '~/components/icon/ArrowDown.vue'
import iconUser from '~/components/icon/User.vue'
import iconLogout from '~/components/icon/Logout.vue'
import iconFolder from '~/components/icon/Folder.vue'
import iconCheckbox from '~/components/icon/Checkbox.vue'
import iconPlus from '~/components/icon/Plus.vue'
import MapSettings from '~/components/MapSettings.vue'
import Button from '~/components/general/Button.vue'
import DeviceItem from '~/components/DeviceItem.vue'
import GeneralCheckbox from '~/components/general/Checkbox.vue'
import GeneralCollapse from '~/components/general/Collapse.vue'
import GeneralTextInput from '~/components/general/TextInput.vue'
import GeneralDropdown from '~/components/general/Dropdown.vue'
import GeneralNavigationLink from '~/components/general/NavigationLink.vue'

// Vue Virtual Scroller
import { DynamicScroller } from 'vue-virtual-scroller'
import { DynamicScrollerItem } from 'vue-virtual-scroller'
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'

const store = useMapStore()
const $auth = useAuthStore()
const { data } = useAuth()
const showFilterDropdown = ref(false)
const menuMapType = ref(false)
const menuMapSettings = ref(false)

interface StatusConfig {
  [key: string]: {
    color: string;
    label: string;
  }
}

const statusConfig: StatusConfig = {
  online: { color: 'bg-green-500', label: 'Online' },
  offline: { color: 'bg-red-500', label: 'Offline' },
  ack: { color: 'bg-gray-500', label: 'ACK' },
  idle: { color: 'bg-yellow-500', label: 'Idle' }
}

const ungroupedDevicesCount = computed(() => store.ungroupedDevices.length)

const groupedDevicesCount = computed(() => {
  return store.filteredGroups.reduce((total, group) => {
    return total + store.getDevicesInGroup(group.id).length
  }, 0)
})

const sortDisplay = computed(() => {
  switch (store.sortBy) {
    case 'name-desc':
      return 'Name (Z-A)'
    case 'status':
      return 'Status'
    case 'last-updated':
      return 'Last Updated'
    default:
      return 'Name (A-Z)'
  }
})

const activeTab = ref('devices')

const links = computed(() => {
  const { isMdvrConnected } = useMdvrConnection()



  const allLinks = [
    { id: 'menuDashboard', name: 'Dashboard', destination: '/dashboard', icon: iconBarChart, isVisible: true },
    { id: 'menuEvidence', name: 'Evidence', destination: '/evidence', icon: iconFolder, isVisible: isMdvrConnected.value },
    { id: 'menuSettings', name: 'Settings', destination: '/settings', icon: iconSettings, isVisible: true },
  ]

  const visibleLinks = allLinks.filter(i => i.isVisible)
  // console.log('DeviceSidebar - Visible links:', visibleLinks.map(l => l.name))

  return visibleLinks
})

const sidebarWidth = computed(() => {
  if (!store.isSidebarOpen) return 64
  return 256 // Slightly wider for better text display
})


const groupOptions = computed(() => {
  return store.groups
    .filter(group => group && typeof group.id !== 'undefined')
    .map(group => ({
      id: group.id,
      name: group.name
    }))
})

const routePath = computed((): string => {
  return useRoute().path
})

const getColor = (destination: string) => {
  return routePath.value.startsWith(destination) ? 'stroke-black' : 'stroke-white'
}

const isMobile = () => {
  return window.innerWidth <= 640
}

const handleResize = () => {
  if (window.innerWidth <= 768) {
    store.isSidebarOpen = false
    store.isSidebarMenuOpen = false
  } else {
    store.isSidebarOpen = false
    store.isSidebarMenuOpen = true
  }
};

const toggleSidebar = () => {
  const isMobile = window.innerWidth <= 640
  store.isSidebarOpen = !store.isSidebarOpen

  if (isMobile && store.isSidebarOpen) {
    store.isSidebarMenuOpen = false
  }
};

const toggleMenuMapType = () => {
  menuMapSettings.value = false
  menuMapType.value = !menuMapType.value
}

const toggleMenuMapSettings = () => {
  menuMapType.value = false
  menuMapSettings.value = !menuMapSettings.value
}

onMounted(() => {
  isMobile()
  handleResize()
  window.addEventListener('resize', handleResize);
  window.addEventListener('resize', isMobile);
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    const filterButton = document.querySelector('.relative.inline-block')
    if (showFilterDropdown.value && filterButton && !filterButton.contains(target)) {
      showFilterDropdown.value = false
    }
  })
})

onUnmounted(() => {
  document.removeEventListener('click', () => {
  })
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}

:deep(.device-list) {
  @apply space-y-4;
}

:deep(.device-group) {
  @apply py-2 first:pt-0 last:pb-0;
}

:deep(.collapse-trigger) {
  @apply transition-colors duration-200;
}

:deep(.collapse-content) {
  @apply transition-all duration-200;
}

.filter-dropdown {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.relative.inline-block {
  position: relative;
  display: inline-block;
}

.hover\:stroke-black:hover {
  stroke: black !important;
}
</style>
