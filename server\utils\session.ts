import { H3Event, getCookie, createError } from 'h3'
import { getSession as getTraccarSession } from './traccar'
import { db } from '../database/drizzle'
import { users, telematicsUserData, integrationTokens, mdvrUserData } from '../database/schema'
import { eq, and, gt } from 'drizzle-orm'

export interface SessionUser {
	id: string
	email: string
}

export interface SessionUserData {
	traccar: null | SessionUser,
	telematics: null | SessionUser,
	mdvr: null | SessionUser
}

export async function requireUserSession(event: H3Event): Promise<SessionUserData> {
	const traccarSessionId = getCookie(event, 'traccar.token')
	const telematicsSessionId = getCookie(event, 'telematics.token')
	const mdvrSessionId = getCookie(event, 'mdvr.token')

	if (!traccarSessionId && !telematicsSessionId && !mdvrSessionId) {
		throw createError({
			statusCode: 401,
			statusMessage: 'Unauthorized - No valid session found'
		})
	}

	const userData: SessionUserData = {
		traccar: null,
		telematics: null,
		mdvr: null
	}

	try {
		if (traccarSessionId) {
			const sessionData = await getTraccarSession(traccarSessionId)
			userData.traccar = { id: sessionData.id, email: sessionData.email }
		}

		if (telematicsSessionId) {
			userData.telematics = (await db
				.select({ id: users.id, email: users.email })
				.from(telematicsUserData)
				.innerJoin(users, eq(telematicsUserData.userId, users.id))
				.where(eq(telematicsUserData.apiHash, telematicsSessionId))
				.limit(1)
			)[0]
		}

		if (mdvrSessionId) {
			const user = await db.query.mdvrUserData.findMany()

			console.log('MDVR Data', user.map(u => u.userId))

			userData.mdvr = (await db
				.select({ id: users.id, email: users.email })
				.from(mdvrUserData)
				.innerJoin(users, eq(mdvrUserData.userId, users.id))
				.where(eq(mdvrUserData.token, mdvrSessionId))
				.limit(1)
			)[0]
		}

		console.log(userData)

		return userData
	} catch (_) {
		throw createError({
			statusCode: 401,
			statusMessage: 'No valid session type found'
		})
	}
}