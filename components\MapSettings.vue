<template>
  <div class="divide-y divide-gray-100 p-4">
    <!-- Vessel Status -->
    <div class="py-4 first:pt-0">
      <h3 class="text-sm font-medium text-gray-700 mb-3">Vessel Status</h3>
      <div class="space-y-3 bg-gray-50 rounded-lg p-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 rounded-full bg-green-500"/>
            <span class="text-sm">Show Online Vessel</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.vesselStatus.showOnline"
              @update:model-value="() => store.toggleVesselStatus('showOnline')"
          />
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 rounded-full bg-red-500"/>
            <span class="text-sm">Show Offline Vessel</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.vesselStatus.showOffline"
              @update:model-value="() => store.toggleVesselStatus('showOffline')"
          />
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div class="w-2 h-2 rounded-full bg-yellow-500"/>
            <span class="text-sm">Show Waiting Data Vessel</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.vesselStatus.showWaiting"
              @update:model-value="() => store.toggleVesselStatus('showWaiting')"
          />
        </div>
      </div>
    </div>

    <!-- Vessel Element -->
    <div class="py-4">
      <h3 class="text-sm font-medium text-gray-700 mb-3">Vessel Element</h3>
      <div class="space-y-3 bg-gray-50 rounded-lg p-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <span class="text-sm">Show Vessel Label</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.vesselElement.showLabel"
              @update:model-value="() => store.toggleVesselElement('showLabel')"
          />
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <span class="text-sm">Show Vessel Trail</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.vesselElement.showVesselTrail"
              @update:model-value="() => store.toggleVesselElement('showVesselTrail')"
          />
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <span class="text-sm">Show Dot Trail</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.vesselElement.showDotTrail"
              @update:model-value="() => store.toggleVesselElement('showDotTrail')"
          />
        </div>
      </div>
    </div>
    <div class="py-4">
      <h3 class="text-sm font-medium text-gray-700 mb-3">Marine Element</h3>
      <div class="space-y-3 bg-gray-50 rounded-lg p-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <span class="text-sm">Show Sea Marks</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.overlayLayers.showSeaMarks"
              @update:model-value="() => store.toggleOverlayLayer('showSeaMarks')"
          />
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <span class="text-sm">Show Marine Traffic</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.overlayLayers.showMarineTraffic"
              @update:model-value="() => store.toggleOverlayLayer('showMarineTraffic')"
          />
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <span class="text-sm">Show Depth Contours</span>
          </div>
          <general-toggle
              :model-value="store.mapSettings.overlayLayers.showDepthContours"
              @update:model-value="() => store.toggleOverlayLayer('showDepthContours')"
          />
        </div>
      </div>
    </div>

    <!-- Trail Settings -->
    <div class="py-4">
      <h3 class="text-sm font-medium text-gray-700 mb-3">Trail Settings</h3>
      <div class="space-y-4 bg-gray-50 rounded-lg p-3">
        <div>
          <div class="flex justify-between mb-2">
            <span class="text-sm">Trail Width</span>
            <span class="text-sm font-medium">{{ store.mapSettings.trailSettings.width }}</span>
          </div>
          <general-range-slider
              :model-value="store.mapSettings.trailSettings.width"
              @update:model-value="(value) => store.updateTrailSettings({ width: value })"
              :min="1"
              :max="10"
              :step="1"
          />
        </div>
        <div>
          <div class="flex justify-between mb-2">
            <span class="text-sm">Trail Opacity</span>
            <span class="text-sm font-medium">{{ store.mapSettings.trailSettings.opacity }}</span>
          </div>
          <general-range-slider
              :model-value="store.mapSettings.trailSettings.opacity"
              @update:model-value="(value) => store.updateTrailSettings({ opacity: value })"
              :min="0"
              :max="1"
              :step="0.1"
          />
        </div>
      </div>
    </div>

    <!-- Map Type -->
<!--    <div class="py-4 last:pb-0">-->
<!--      <h3 class="text-sm font-medium text-gray-700 mb-3">Map Type</h3>-->
<!--      <div class="grid grid-cols-2 gap-3">-->
<!--        <button-->
<!--            class="p-3 border rounded-lg flex flex-col items-center gap-2"-->
<!--            :class="[store.mapSettings.mapType === 'satellite' ? 'border-primary-500 bg-primary-50' : 'border-gray-200']"-->
<!--            @click="store.setMapType('satellite')"-->
<!--        >-->
<!--          <div class="w-14 h-14 bg-gray-900 rounded-lg"/>-->
<!--          <span class="text-sm">Satellite</span>-->
<!--        </button>-->
<!--        <button-->
<!--            class="p-3 border rounded-lg flex flex-col items-center gap-2"-->
<!--            :class="[store.mapSettings.mapType === 'vector' ? 'border-primary-500 bg-primary-50' : 'border-gray-200']"-->
<!--            @click="store.setMapType('vector')"-->
<!--        >-->
<!--          <div class="w-14 h-14 bg-blue-100 rounded-lg"/>-->
<!--          <span class="text-sm">Vector</span>-->
<!--        </button>-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script setup lang="ts">
import {useMapStore} from '~/store/map'
import GeneralToggle from '~/components/general/Toggle.vue'
import GeneralRangeSlider from '~/components/general/RangeSlider.vue'

const store = useMapStore()
</script>