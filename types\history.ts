export interface TelematicsHistoryItem {
	id: number
	device_id: number
	item_id: string
	time: string
	raw_time: string
	altitude: number
	course: number
	speed: number
	latitude: number
	longitude: number
	distance: number
	other: string
	other_arr: string[]
	valid: number
	device_time: string
	server_time: string
	sensors_data: {
		id: string
		value: number
	}[]
}

export interface TelematicsHistoryGroup {
	status: number
	time: string | null
	show: string
	raw_time: string
	distance: number
	time_seconds?: number
	engine_work?: number
	engine_idle?: number
	engine_hours?: number
	fuel_consumption?: number | null
	top_speed?: number
	average_speed?: number
	items: TelematicsHistoryItem[]
}

export interface TelematicsHistoryResponse {
	items: TelematicsHistoryGroup[]
}