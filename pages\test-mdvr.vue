<template>
	<div class="container mx-auto p-6">
		<h1 class="text-2xl font-bold mb-6">MDVR Integration Test Page</h1>
		
		<!-- Connection Status Section -->
		<div class="bg-white rounded-lg shadow p-6 mb-6">
			<h2 class="text-lg font-semibold mb-4">Connection Status</h2>
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div class="space-y-2">
					<div class="flex items-center gap-2">
						<span class="font-medium">Has MDVR Token:</span>
						<span :class="hasMdvrToken ? 'text-green-600' : 'text-red-600'">
							{{ hasMdvrToken ? 'Yes' : 'No' }}
						</span>
					</div>
					<div class="flex items-center gap-2">
						<span class="font-medium">Connection Status:</span>
						<span :class="getStatusColor(connectionStatus)">
							{{ connectionStatus }}
						</span>
					</div>
					<div class="flex items-center gap-2">
						<span class="font-medium">Is Connected:</span>
						<span :class="isMdvrConnected ? 'text-green-600' : 'text-red-600'">
							{{ isMdvrConnected ? 'Yes' : 'No' }}
						</span>
					</div>
					<div class="flex items-center gap-2">
						<span class="font-medium">Last Checked:</span>
						<span class="text-gray-600">
							{{ lastChecked ? new Date(lastChecked).toLocaleString() : 'Never' }}
						</span>
					</div>
				</div>
			</div>
			
			<div class="mt-4 space-x-2">
				<Button @click="refreshStatus" :loading="refreshing" variant="primary">
					Refresh Status
				</Button>
				<Button @click="testEvidenceAPI" :loading="testingAPI" variant="secondary">
					Test Evidence API
				</Button>
			</div>
		</div>

		<!-- Sidebar Menu Test Section -->
		<div class="bg-white rounded-lg shadow p-6 mb-6">
			<h2 class="text-lg font-semibold mb-4">Sidebar Menu Visibility Test</h2>
			<div class="space-y-2">
				<p class="text-sm text-gray-600">
					The Evidence menu should only be visible when MDVR is connected.
				</p>
				<div class="flex items-center gap-2">
					<span class="font-medium">Evidence Menu Visible:</span>
					<span :class="isMdvrConnected ? 'text-green-600' : 'text-red-600'">
						{{ isMdvrConnected ? 'Yes (Should be visible)' : 'No (Should be hidden)' }}
					</span>
				</div>
				<div class="mt-4 p-4 bg-gray-50 rounded">
					<h3 class="font-medium mb-2">Debug Information:</h3>
					<div class="text-sm space-y-1">
						<div>Current Route: {{ $route.path }}</div>
						<div>Layout: {{ $route.meta.layout || 'default' }}</div>
						<div>Check browser console for sidebar debug logs</div>
					</div>
				</div>
				<div class="mt-4">
					<p class="text-sm text-gray-600 mb-2">
						<strong>Instructions:</strong> Open browser console (F12) and look for logs starting with "DeviceSidebar" or "AppSidebar" to see which sidebar is being used and what the connection status is.
					</p>
				</div>
			</div>
		</div>

		<!-- API Test Results -->
		<div v-if="apiTestResult" class="bg-white rounded-lg shadow p-6">
			<h2 class="text-lg font-semibold mb-4">API Test Results</h2>
			<div class="space-y-2">
				<div class="flex items-center gap-2">
					<span class="font-medium">Status:</span>
					<span :class="apiTestResult.success ? 'text-green-600' : 'text-red-600'">
						{{ apiTestResult.success ? 'Success' : 'Failed' }}
					</span>
				</div>
				<div class="flex items-center gap-2">
					<span class="font-medium">Response:</span>
					<span class="text-gray-600">{{ apiTestResult.message }}</span>
				</div>
				<div v-if="apiTestResult.shouldShowOverlay" class="text-orange-600">
					⚠️ This should trigger the authentication overlay
				</div>
			</div>
		</div>

		<!-- Test Authentication Overlay -->
		<div class="bg-white rounded-lg shadow p-6 mt-6">
			<h2 class="text-lg font-semibold mb-4">Authentication Overlay Test</h2>
			<Button @click="showAuthOverlay" variant="warning">
				Show MDVR Auth Overlay
			</Button>
		</div>

		<!-- MDVR Authentication Overlay -->
		<MDVRAuthOverlay
			@mounted="onMDVRAuthOverlayMounted"
			@auth-success="handleMDVRAuthSuccess"
		/>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Button from '~/components/general/Button.vue'
import MDVRAuthOverlay from '~/components/evidence/MDVRAuthOverlay.vue'
import type { ElementEvent } from '~/types/element'
import { toast } from 'vue3-toastify'

// Use the MDVR connection composable
const { 
	hasMdvrToken, 
	connectionStatus, 
	lastChecked, 
	isMdvrConnected, 
	refreshConnectionStatus 
} = useMdvrConnection()

const refreshing = ref(false)
const testingAPI = ref(false)
const apiTestResult = ref<any>(null)
const mdvrAuthOverlay = ref<ElementEvent | null>(null)

const getStatusColor = (status: string) => {
	switch (status) {
		case 'connected': return 'text-green-600'
		case 'disconnected': return 'text-red-600'
		case 'checking': return 'text-yellow-600'
		default: return 'text-gray-600'
	}
}

const refreshStatus = async () => {
	refreshing.value = true
	try {
		await refreshConnectionStatus()
		toast.success('Connection status refreshed')
	} catch (error) {
		toast.error('Failed to refresh status')
	} finally {
		refreshing.value = false
	}
}

const testEvidenceAPI = async () => {
	testingAPI.value = true
	apiTestResult.value = null
	
	try {
		const response = await $fetch('/api/mdvr/evidence', {
			method: 'POST',
			body: {
				startTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
				endTime: new Date().toISOString()
			}
		})
		
		apiTestResult.value = {
			success: true,
			message: `API call successful. Status: ${response.status}`,
			shouldShowOverlay: false
		}
		toast.success('Evidence API test successful')
	} catch (error: any) {
		const isAuthError = error.statusCode === 401 || 
							error.status === 401 || 
							error.message?.includes('Unauthorized')
		
		apiTestResult.value = {
			success: false,
			message: error.message || 'API call failed',
			shouldShowOverlay: isAuthError
		}
		
		if (isAuthError) {
			toast.warning('Authentication error detected - overlay should appear')
		} else {
			toast.error('API test failed')
		}
	} finally {
		testingAPI.value = false
	}
}

const showAuthOverlay = () => {
	mdvrAuthOverlay.value?.show()
}

const onMDVRAuthOverlayMounted = (event: ElementEvent) => {
	mdvrAuthOverlay.value = event
}

const handleMDVRAuthSuccess = async () => {
	await refreshStatus()
	toast.success('MDVR authentication successful!')
}
</script>
