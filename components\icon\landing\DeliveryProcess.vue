<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.25 12.1875H18.5625V16.1925L16.875 15.2175L15.1875 16.1925V12.1875H13.5C12.7763 12.1875 12.1875 12.7763 12.1875 13.5V20.25C12.1875 20.9737 12.7763 21.5625 13.5 21.5625H20.25C20.9737 21.5625 21.5625 20.9737 21.5625 20.25V13.5C21.5625 12.7763 20.9737 12.1875 20.25 12.1875ZM15 18.5625H18.75C18.9937 18.5625 18.9937 18.9375 18.75 18.9375H15C14.7563 18.9375 14.7563 18.5625 15 18.5625ZM20.25 20.4375H13.5C13.2563 20.4375 13.2563 20.0625 13.5 20.0625H20.25C20.4937 20.0625 20.4937 20.4375 20.25 20.4375Z" fill="#0D0000"/>
    <path d="M16.875 14.7825L18.1875 15.54V12.1875H15.5625V15.54L16.875 14.7825Z" fill="#0D0000"/>
    <path d="M18.1875 6.9375H14.9813C14.8913 7.3725 14.5088 7.6875 14.0625 7.6875H6.5625C6.11625 7.6875 5.73375 7.3725 5.64375 6.9375H2.4375V20.25C2.4375 20.9737 3.02625 21.5625 3.75 21.5625H12.4387C12.0412 21.2437 11.8125 20.76 11.8125 20.25V19.3125H8.25C8.00625 19.3125 8.00625 18.9375 8.25 18.9375H11.8125V17.8125H8.25C8.00625 17.8125 8.00625 17.4375 8.25 17.4375H11.8125V15.1875H8.25C8.00625 15.1875 8.00625 14.8125 8.25 14.8125H11.8125V13.6875H8.25C8.00625 13.6875 8.00625 13.3125 8.25 13.3125H11.8238C11.9175 12.4575 12.6413 11.8125 13.5 11.8125H18.1875V6.9375ZM6.75 19.5H4.5V17.25H6.75V19.5ZM6.75 15.375H4.5V13.125H6.75V15.375ZM6.75 11.25H4.5V9H6.75V11.25ZM16.125 11.0625H8.25C8.00625 11.0625 8.00625 10.6875 8.25 10.6875H16.125C16.3687 10.6875 16.3687 11.0625 16.125 11.0625ZM16.125 9.5625H8.25C8.00625 9.5625 8.00625 9.1875 8.25 9.1875H16.125C16.3687 9.1875 16.3687 9.5625 16.125 9.5625Z" fill="#0D0000"/>
    <path d="M18.1875 6C18.1875 5.27625 17.5987 4.6875 16.875 4.6875H15V6.5625H18.1875V6Z" fill="#0D0000"/>
    <path d="M8.63625 3.93756H6.5625C6.25125 3.93756 6 4.18881 6 4.50006V6.75006C6 7.06131 6.25125 7.31256 6.5625 7.31256H14.0625C14.3737 7.31256 14.625 7.06131 14.625 6.75006V4.50006C14.625 4.18881 14.3737 3.93756 14.0625 3.93756H11.9887C11.895 3.10131 11.1713 2.44506 10.3275 2.43756C9.4725 2.43006 8.73 3.08631 8.63625 3.93756Z" fill="#0D0000"/>
    <path d="M2.4375 6.5625H5.625V4.6875H3.75C3.02625 4.6875 2.4375 5.27625 2.4375 6V6.5625Z" fill="#0D0000"/>
  </svg>

</template>

<style scoped>

</style>
