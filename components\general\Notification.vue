<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  isShow: {
    type: Boolean,
    default: false
  },
  color: {
    type: String as () => 'primary' | 'success' | 'warning' | 'info' | 'error',
    default: 'primary'
  },
  isClosable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:modelValue', 'on-click-close'])

function onClickClose() {
  emit('update:modelValue', false)
  emit('on-click-close')
}
</script>

<template>
  <div
    v-if="modelValue || isShow"
    class="p-4 space-x-4 w-full flex justify-between items-center rounded-lg border"
    :class="`bg-${color}-100 text-${color}-500 border-${color}-500`"
  >
    <div class="space-x-4 flex items-center justify-start w-full">
      <slot name="prefix"/>
      <slot name="default"/>
    </div>

    <div
      v-if="props.isClosable"
      class="p-1 rounded-full cursor-pointer transition-all"
      :class="`hover:bg-${color}-200`"
      @click="onClickClose()"
    >
      <icon-close size="20" :class="`stroke-${color}-500`"/>
    </div>
  </div>
</template>

<style scoped>

</style>