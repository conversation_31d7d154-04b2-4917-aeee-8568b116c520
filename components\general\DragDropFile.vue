<script setup lang="ts">
import {toast} from "vue3-toastify";

const props = defineProps({
  modelValue: {
    type: [Object as () => null | File, Array as () => File[]],
    default: null
  },
  id: {
    type: String,
    default: 'select-file'
  },
  label: {
    type: [String, Object as () => null],
    default: null
  },
  subtitle: {
    type: String,
    default: null
  },
  src: {
    type: String,
    default: null
  },
  maxFileSize: {
    type: Number,
    default: 5
  },
  validFileExtensions: {
    type: Array as () => string[],
    default: []
  },
  isMultiple: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
})

const fileInput = ref<HTMLInputElement | null>(null)
const isFileHovering = ref<boolean>(false)
const inActiveTimeout = ref<null | NodeJS.Timeout>(null)

const emit = defineEmits(['update:modelValue'])

const fileProperties = computed(() => {
  const isImage = (ext: string) => {
    return ext === '.png' || ext === '.jpg' || ext === '.jpeg'
  }

  try {
    if (Array.isArray(props.modelValue)) {
      return props.modelValue.map(file => ({
        name: file.name,
        size: ((file?.size ?? 0) / 1024 / 1024).toFixed(2),
        extension: `.${file.name.split('.').pop()}`,
        isImage: isImage(`.${file.name.split('.').pop()}`),
        url: URL.createObjectURL(file as Blob)
      }))
    } else {
      return {
        name: props.modelValue?.name ?? '',
        size: ((props.modelValue?.size ?? 0) / 1024 / 1024).toFixed(2),
        extension: `.${props.modelValue?.name.split('.').pop()}`,
        isImage: isImage(`.${props.modelValue?.name.split('.').pop()}`),
        url: URL.createObjectURL(props.modelValue as Blob)
      }
    }
  } catch (_) {
    return null
  }
})

const isValidFile = (file: File) => {
  if (file.type && props.validFileExtensions.length > 0) {
    const fileExtension = `.${file.name.split('.').pop()}`
    if (!props.validFileExtensions.find(vfe => vfe === fileExtension)) {
      toast.error('Invalid file extension')
      return false
    }
  }

  if (file.size > props.maxFileSize * 1024 * 1024) {
    toast.error(`File must be less than ${props.maxFileSize} MB`)
    return false
  }

  return true
}

function onDragging(isHovering: boolean) {
  isFileHovering.value = isHovering

  if (isHovering) {
    if (inActiveTimeout.value !== null) {
      clearTimeout(inActiveTimeout.value)
    }
  } else {
    inActiveTimeout.value = setTimeout(() => {
      isFileHovering.value = isHovering
    }, 50)
  }
}

function onChangeFile(event: Event) {
  if (props.isMultiple) {
    const files: File[] = [...event.target?.files]
    if (files.every(file => isValidFile(file)))
      emit('update:modelValue', [...(props.modelValue as File[]).concat(...files)])
  } else {
    const file: File = event.target?.files[0]
    if (isValidFile(file)) emit('update:modelValue', file)
  }
}

function onDropFile(event: DragEvent) {
  isFileHovering.value = false

  if (props.isMultiple) {
    const files: File[] = [...event.dataTransfer?.files!]

    if (files.length === 0) return
    if (files.every(file => isValidFile(file)))
      emit('update:modelValue', [...(props.modelValue as File[]).concat(...files)])
  } else {
    const file = event.dataTransfer?.files[0]

    if (!file) return
    if (isValidFile(file)) emit('update:modelValue', file)
  }
}
</script>

<template>
  <div class="space-y-2">
    <label v-if="props.label" :for="props.id" class="mb-1.5 text-sm font-medium text-gray-700">
      {{ props.label }}
      <span v-if="props.required" class="text-primary-500">*</span>
    </label>

    <div v-if="props.isMultiple || !props.modelValue">
      <input
        :id="props.id"
        hidden
        ref="fileInput"
        type="file"
        :multiple="props.isMultiple"
        :accept="props.validFileExtensions.join(',')"
        @change="onChangeFile"
      >

      <div
        class="p-4 stroke-gray-700 border border-gray-300 rounded-lg flex flex-col items-center justify-center transition-all space-y-3 cursor-pointer hover:bg-primary-25 hover:border-primary-200"
        :class="isFileHovering ? 'bg-primary-25 border-primary-200' : ''"
        @click="fileInput?.click()"
        @dragover.prevent="onDragging(true)"
        @dragleave.prevent="onDragging(false)"
        @drop.prevent="onDropFile"
      >
        <div class="p-2 aspect-square w-min rounded-full bg-primary-50">
          <div class="p-2 aspect-square w-min rounded-full bg-primary-100">
            <icon-upload-cloud size="20" class="stroke-primary-500"/>
          </div>
        </div>

        <div class="space-y-1">
          <p v-if="!isFileHovering" class="text-sm text-gray-500 text-center">
            <span class="font-semibold text-primary-500">Click to upload</span> or drag and drop
          </p>
          <p v-else class="text-sm font-semibold text-primary-500">Release to drop file here</p>
          <p v-if="props.subtitle" class="text-sm text-gray-500 text-center">{{ props.subtitle }}</p>
        </div>
      </div>
    </div>

    <div>
      <div v-if="Array.isArray(fileProperties)" class="mt-4 space-y-2">
        <general-file-button
          v-for="(file, i) in fileProperties"
          :title="file?.name"
          :subtitle="`${file?.size} MB`"
          @on-click="emit('update:modelValue', [...(props.modelValue as File[]).slice(0, i), ...(props.modelValue as File[]).slice(i + 1)])"
        >
          <template v-if="file?.isImage" #prefix>
            <general-avatar :src="file?.url"/>
          </template>

          <template #suffix>
            <icon-trash size="20" class="stroke-error-500"/>
          </template>
        </general-file-button>
      </div>

      <general-file-button
        v-else-if="fileProperties"
        :title="fileProperties?.name"
        :subtitle="`${fileProperties?.size} MB`"
        @on-click="emit('update:modelValue', null)"
      >
        <template v-if="fileProperties?.isImage" #prefix>
          <general-avatar :src="fileProperties?.url"/>
        </template>

        <template #suffix>
          <icon-trash size="20" class="stroke-error-500"/>
        </template>
      </general-file-button>
    </div>
  </div>
</template>

<style scoped>

</style>