<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: 27
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size"  viewBox="0 0 27 27" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.87466 0.15105C7.34743 0.829336 5.07056 2.2502 3.38457 4.20102C2.49674 5.22833 2.01505 5.91407 1.50703 6.87394C0.488357 8.79858 0.00308261 10.7962 1.0792e-05 13.078C-0.00196395 14.4968 0.267259 16.2069 0.649115 17.2014L0.726276 17.4025H3.11293C4.99471 17.4025 5.49118 17.3832 5.45987 17.3111C5.43801 17.2609 5.30453 16.9564 5.16322 16.6346C4.32996 14.7368 4.23144 12.3505 4.90161 10.2995C5.72054 7.79359 7.86745 5.54567 10.2014 4.75051L10.5671 4.62596L10.5864 2.31076C10.6017 0.481636 10.5863 -0.00349177 10.5132 1.88774e-05C10.4624 0.00243245 10.175 0.070378 9.87466 0.15105ZM20.5505 4.67218V7.60057L20.8853 8.08958C21.8537 9.50415 22.351 11.0215 22.4273 12.7948C22.5821 16.3917 20.6311 19.651 17.3689 21.2455C14.5546 22.6211 11.1847 22.4187 8.61697 20.7197C8.32186 20.5244 8.03925 20.3383 7.98893 20.3061C7.91645 20.2598 7.89751 20.7768 7.89751 22.8056V25.3635L8.64718 25.6552C12.1162 27.0046 16.2606 26.8122 19.4816 25.1521C22.967 23.3559 25.5422 20.177 26.4765 16.5178C26.9809 14.5424 26.981 11.8523 26.4769 9.87789C26.0084 8.04313 25.021 6.08112 23.8408 4.63985C23.0915 3.72496 21.9563 2.69883 20.8979 1.97988L20.5505 1.74386V4.67218Z" fill="#0D0000"/>
    <path d="M20.335 13.2349C20.335 17.0663 17.2289 20.1724 13.3975 20.1724C9.56599 20.1724 6.45996 17.0663 6.45996 13.2349C6.45996 9.40339 9.56599 6.29736 13.3975 6.29736C17.2289 6.29736 20.335 9.40339 20.335 13.2349Z" fill="#EF3434"/>
    </svg>
    
</template>

<style scoped> </style>
