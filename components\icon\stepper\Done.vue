<script setup lang="ts">
const props = defineProps({
  size: {
    type: Number,
    default: 40
  }
})
</script> 

<template>
  <div>
    <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 40 40" fill="none">
      <path d="M1.625 20C1.625 9.85177 9.85177 1.625 20 1.625C30.1482 1.625 38.375 9.85177 38.375 20C38.375 30.1482 30.1482 38.375 20 38.375C9.85177 38.375 1.625 30.1482 1.625 20Z" fill="var(--primary-500)"/>
      <path d="M1.625 20C1.625 9.85177 9.85177 1.625 20 1.625C30.1482 1.625 38.375 9.85177 38.375 20C38.375 30.1482 30.1482 38.375 20 38.375C9.85177 38.375 1.625 30.1482 1.625 20Z" stroke="var(--primary-100)" stroke-width="3.25"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M27.9157 12.4441L15.979 23.627L12.8115 20.3418C12.228 19.8077 11.311 19.7753 10.6442 20.2285C9.99401 20.6978 9.81062 21.5232 10.2107 22.1867L13.9618 28.1099C14.3286 28.6601 14.9621 29 15.6789 29C16.3625 29 17.0126 28.6601 17.3794 28.1099C17.9796 27.3493 29.4328 14.0949 29.4328 14.0949C30.9332 12.606 29.116 11.2951 27.9157 12.428V12.4441Z" fill="white"/>
    </svg>
  </div>
</template>

<style scoped></style>