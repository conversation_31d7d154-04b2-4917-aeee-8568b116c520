<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  items: {
    type: Array as () => { text: string, value: string }[],
    default: []
  }
})

const emit = defineEmits(['update:model-value'])

const buttonClasses = (value: string) => {
  const baseClass = 'py-2.5 px-4 transition'
  const color = props.modelValue === value ? 'bg-primary-50 text-primary-500' : 'hover:bg-gray-100'
  return `${baseClass} ${color}`
}
</script>

<template>
  <div class="grid border divide-x rounded-lg overflow-hidden" :class="`grid-cols-${items.length}`">
    <button v-for="(item, i) in props.items" :class="buttonClasses(item.value)"
            @click="emit('update:model-value', item.value)">
      {{ item.text }}
    </button>
  </div>
</template>

<style scoped></style>