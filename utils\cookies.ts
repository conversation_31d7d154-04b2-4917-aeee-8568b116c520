import { useCookie } from '#app'

interface SessionData {
    traccarSession?: string;
    telematicsSession?: string;
}

export function getCookie(key: string) {
    return useCookie(key, {
        maxAge: 60 * 60 * 24, // 24 hours
        sameSite: 'lax',
        secure: process.env.NODE_ENV === 'production'
    })
}

export function setCookie(key: string, value: any) {
    const cookie = getCookie(key)
    cookie.value = value
}

export function clearCookies() {
    const cookies: SessionData = {
        traccarSession: undefined,
        telematicsSession: undefined
    }

    Object.entries(cookies).forEach(([key]) => {
        setCookie(`${key}-session-id`, undefined)
    })

    // Clear auth cookies
    setCookie('auth.token', undefined)
    setCookie('session_token', undefined)
}