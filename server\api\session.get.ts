import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3';
import { db } from '../database/drizzle'
import { users } from '../database/schema';
import { eq } from 'drizzle-orm'

export default defineEventHandler(async (event) => {
    const userId = getCookie(event, 'user_id')

    if (!userId) {
        throw createError({
            statusCode: 401,
            statusMessage: 'Unauthorized'
        })
    }

    const userWithTokens = await db.query.users.findFirst({
        where: eq(users.id, userId),
        with: {
            integrationTokens: true,
        },
        columns: {
            id: true,
            email: true,
        }
    })

    if (!userWithTokens) return null

    // Re-init cookie if necessary
    const cookieToken = {
        traccar: getCookie(event, 'traccar.token'),
        telematics: getCookie(event, 'telematics.token'),
        mdvr: getCookie(event, 'mdvr.token')
    }

    const userToken = {
        traccar: userWithTokens.integrationTokens.find(i => i.provider === 'traccar')?.token,
        telematics: userWithTokens.integrationTokens.find(i => i.provider === 'telematics')?.token,
        mdvr: userWithTokens.integrationTokens.find(i => i.provider === 'mdvr')?.token
    }

    if (!cookieToken.traccar && userToken.traccar)
        setCookie(event, 'traccar.token', userToken.traccar, { httpOnly: true, path: '/' })

    if (!cookieToken.telematics && userToken.telematics)
        setCookie(event, 'telematics.token', userToken.telematics, { httpOnly: true, path: '/' })

    if (!cookieToken.mdvr && userToken.mdvr)
        setCookie(event, 'mdvr.token', userToken.mdvr, { httpOnly: true, path: '/' })

    const initialProviders = userWithTokens.integrationTokens
        .filter(i => i.userId === userWithTokens.id && i.username === userWithTokens.email)
        .map(i => i.provider)

    return { ...userWithTokens, initialProviders }
});

