<template>
  <div class="virtual-list-container" ref="container">
    <div 
      class="virtual-list-content" 
      :style="{ height: totalHeight + 'px' }"
    >
      <div 
        class="virtual-list-items"
        :style="{ transform: `translateY(${offsetY}px)` }"
      >
        <div 
          v-for="item in visibleItems" 
          :key="item.id"
          :style="{ height: itemHeight + 'px' }"
          class="virtual-list-item"
        >
          <slot name="item" :device="item"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { UnifiedDevice } from '~/types/device'

const props = defineProps<{
  items: UnifiedDevice[]
  itemHeight: number
  buffer?: number
}>()

const container = ref<HTMLElement | null>(null)
const scrollTop = ref(0)
const viewportHeight = ref(0)
const isScrolling = ref(false)
const scrollTimeout = ref<NodeJS.Timeout | null>(null)

// Increase buffer for smoother scrolling
const buffer = computed(() => props.buffer || 10)

const totalHeight = computed(() => props.items.length * props.itemHeight)

const visibleRange = computed(() => {
  if (!viewportHeight.value) return { start: 0, end: 0 }
  
  const start = Math.floor(scrollTop.value / props.itemHeight)
  const visibleCount = Math.ceil(viewportHeight.value / props.itemHeight)
  
  return {
    start: Math.max(0, start - buffer.value),
    end: Math.min(props.items.length, start + visibleCount + buffer.value)
  }
})

const visibleItems = computed(() => {
  // Only update visible items if not currently scrolling
  if (isScrolling.value) {
    return props.items.slice(visibleRange.value.start, visibleRange.value.end)
  }
  return props.items.slice(visibleRange.value.start, visibleRange.value.end)
})

const offsetY = computed(() => visibleRange.value.start * props.itemHeight)

const handleScroll = () => {
  if (!container.value) return
  
  // Set scrolling state
  isScrolling.value = true
  
  // Update scroll position
  scrollTop.value = container.value.scrollTop
  
  // Clear previous timeout
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }
  
  // Set new timeout
  scrollTimeout.value = setTimeout(() => {
    isScrolling.value = false
  }, 150)
}

const handleResize = () => {
  if (!container.value) return
  viewportHeight.value = container.value.clientHeight
}

// Use ResizeObserver for more efficient resize handling
let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  if (container.value) {
    viewportHeight.value = container.value.clientHeight
    container.value.addEventListener('scroll', handleScroll, { passive: true })
    
    resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(container.value)
  }
})

onUnmounted(() => {
  if (container.value) {
    container.value.removeEventListener('scroll', handleScroll)
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
  }
  if (scrollTimeout.value) {
    clearTimeout(scrollTimeout.value)
  }
})
</script>

<style scoped>
.virtual-list-container {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  will-change: transform;
}

.virtual-list-content {
  position: relative;
  will-change: height;
}

.virtual-list-items {
  position: absolute;
  width: 100%;
  will-change: transform;
}

.virtual-list-item {
  position: relative;
  width: 100%;
}
</style>
