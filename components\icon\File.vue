<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  },
  color: {
    type: String,
    default: '#0D0000'
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none">
    <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z" stroke-width="1.67" stroke-linecap="round" stroke-linejoin="round" />
    <polyline points="13 2 13 9 20 9" stroke-width="1.67" stroke-linecap="round" stroke-linejoin="round" />
  </svg>
</template>

<style scoped>

</style>