<script lang="ts" setup>
import type {DrawerInterface} from "flowbite";
import type {ElementEvent} from "~/types/element";

const windowWidth = ref(0)
const {status} = useAuth()

let drawerSidebar: DrawerInterface | null = null
let modalWarningUnauthorized: ElementEvent | null = null

function resizeHandler() {
  if (process.client) {
    windowWidth.value = window.innerWidth
  }
}

watch(status, (newStatus) => {
  if (newStatus === 'unauthenticated') {
    modalWarningUnauthorized?.show();
  }
})


onMounted(() => {
  if (process.client) {
    window.addEventListener('resize', resizeHandler)
    windowWidth.value = window.innerWidth
  }
})

const isMdAndUp = computed((): boolean => {
  return windowWidth.value >= 768
})

watch(isMdAndUp, () => {
  drawerSidebar?.hide()
})

watch(() => useIsUnauthorized().value, (isUnauthorized) => {
  if (isUnauthorized) modalWarningUnauthorized?.show()
})


function onClickBackToLogin() {
  useCookie('auth:token').value = undefined
  clearCookies()
  window.location.href = '/login'
}
</script>

<template>
  <div class="bg-gray-50">
    <general-modal-warning id="modal-warning-unauthorized" title="Unauthorized"
                          subtitle="User unauthorized, please log in again." button-label="Back to Login"
                          @on-mounted="modalWarningUnauthorized = $event" @on-click-button="onClickBackToLogin">
      <template #icon>
        <illustration-cloud-hourglass class="max-w-[160px] h-fit"/>
      </template>
    </general-modal-warning>
    <div>
      <app-sidebar @on-mounted="drawerSidebar = $event" @on-click-close-sidebar="drawerSidebar?.hide()" />
      <div class="p-10 md:pl-[340px] min-h-screen w-full">
        <layout-header class="mb-6" @on-click-open-sidebar="drawerSidebar?.show()" />
        <Breadcrumbs class="mb-4" />
        <slot name="default" />
      </div>
    </div>
  </div>
</template>
