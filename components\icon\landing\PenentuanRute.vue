<script setup lang="ts">
const props = defineProps({
  size: {
    type: String,
    default: '24'
  }
})
</script>

<template>
  <svg :width="props.size" :height="props.size" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18.686 21.6037L21.0147 18.6862C22.676 16.6008 21.266 13.3987 18.6035 13.2258C17.3468 13.1433 16.1281 13.7883 15.4868 14.8687C14.7818 16.0574 14.8906 17.6024 15.7493 18.6862L18.0781 21.6037C18.2322 21.7949 18.5322 21.7949 18.686 21.6037ZM16.5185 16.5862C16.5185 15.5696 17.366 14.7187 18.3822 14.7187C19.4026 14.7187 20.2497 15.5662 20.2497 16.5862C20.2497 17.6024 19.3985 18.4499 18.3822 18.4499C17.366 18.4499 16.5185 17.6024 16.5185 16.5862Z" fill="#0D0000"/>
    <path d="M6.51758 4.6575L7.64258 5.30625V2.25H5.39258V5.30625L6.51758 4.6575Z" fill="#0D0000"/>
    <path d="M3.5625 10.7812H9.46875C10.1925 10.7812 10.7812 10.1925 10.7812 9.46875V3.5625C10.7812 2.83875 10.1925 2.25 9.46875 2.25H8.0175V5.95875L6.5175 5.0925L5.0175 5.95875V2.25H3.5625C2.83875 2.25 2.25 2.83875 2.25 3.5625V9.46875C2.25 10.1925 2.83875 10.7812 3.5625 10.7812ZM4.875 7.96875H8.15625C8.4 7.96875 8.4 8.34375 8.15625 8.34375H4.875C4.63125 8.34375 4.63125 7.96875 4.875 7.96875ZM3.5625 9.28125H9.46875C9.7125 9.28125 9.7125 9.65625 9.46875 9.65625H3.5625C3.31875 9.65625 3.31875 9.28125 3.5625 9.28125Z" fill="#0D0000"/>
    <path d="M20.25 6.33006H12.735L14.2875 4.77419C14.46 4.60131 14.1938 4.33919 14.0254 4.50756L12.0154 6.51756L14.0254 8.52419C14.1938 8.69256 14.46 8.43044 14.2875 8.25756L12.735 6.70506H20.25C20.9737 6.70506 21.5625 7.29044 21.5625 8.01756V10.0951C21.5625 10.8188 20.9737 11.4076 20.25 11.4076H14.025C13.0913 11.4076 12.3375 12.1613 12.3375 13.0951V15.1726C12.3375 15.8963 11.7487 16.4851 11.025 16.4851H3.75C2.81625 16.4851 2.0625 17.2388 2.0625 18.1726V20.2501C2.0625 21.1838 2.81625 21.9376 3.75 21.9376H16.2787C16.5187 21.9376 16.5187 21.5626 16.2787 21.5626H3.75C3.02625 21.5626 2.4375 20.9738 2.4375 20.2501V18.1726C2.4375 17.4488 3.02625 16.8601 3.75 16.8601H11.025C11.9546 16.8601 12.7125 16.1026 12.7125 15.1726V13.0951C12.7125 12.3679 13.3009 11.7826 14.025 11.7826H20.25C21.1838 11.7826 21.9375 11.0251 21.9375 10.0951V8.01756C21.9375 7.08381 21.1838 6.33006 20.25 6.33006Z" fill="#0D0000"/>
  </svg>
</template>

<style scoped>

</style>
