import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError } from 'h3'
import { requireUserSession } from '../../utils/session'
import { db } from '../../database/drizzle'
import { integrationTokens, users } from '../../database/schema'
import { add } from 'date-fns'
import { eq, and } from 'drizzle-orm'

export default defineEventHandler(async (event) => {
	const userId = getCookie(event, 'user_id')

	if (!userId) {
		throw createError({
			statusCode: 401,
			statusMessage: 'Unauthorized'
		})
	}

	const body = await readBody(event)

	const { username, password, remember } = body

	try {
		const response = await fetch('https://mdvr.transtrack.id/vss/user/login.action', {
			method: 'POST',
			headers: {
				'Accept': 'application/json, text/plain, */*',
				'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
				'Origin': 'https://mdvr.transtrack.id',
				'platform': 'web'
			},
			body: new URLSearchParams({
				username,
				password,
				terminal: '2',
				ssid: '',
				verifyCode: '',
				remeber: remember.toString(),
				scheme: 'https',
				lang: 'en'
			})
		})

		const data = await response.json()

		if (data.status === 10000) {
			// Calculate expiry date (7 days from now)
			const expiry = add(new Date(), { days: 7 })

			// Delete any existing token for this user
			await db.delete(integrationTokens)
				.where(
					and(
						eq(integrationTokens.userId, userId),
						eq(integrationTokens.provider, 'mdvr')
					)
				)

			// Insert new token
			await db.insert(integrationTokens).values({
				userId: userId,
				provider: 'mdvr',
				token: data.data.token,
				pid: data.data.pid,
				guid: data.data.guid,
				username: data.data.username,
				expiry
			})

			setCookie(event, 'mdvr.token', data.data.token, { httpOnly: true, path: '/' })

			return {
				status: 'success',
				message: 'MDVR integration connected successfully'
			}
		} else {
			throw createError({
				statusCode: 401,
				message: data.msg || 'Invalid credentials'
			})
		}
	} catch (error: any) {
		throw createError({
			statusCode: error.statusCode || 500,
			message: error.message || 'Connection failed. Please try again.'
		})
	}
})