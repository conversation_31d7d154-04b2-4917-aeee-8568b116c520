<script setup lang="ts">
import { useAuthStore } from "~/store/auth"
import Button from "~/components/general/Button.vue"

definePageMeta({
  auth: {
    unauthenticatedOnly: true,
    navigateAuthenticatedTo: '/dashboard'
  },
  layout: false,
  layoutTransition: {
    mode: 'out-in'
  }
})

const $auth = useAuthStore()
const showPass = ref(false)
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

async function onSubmitLogin() {
  await $auth.login()
}
</script>

<template>
  <div class="min-h-screen flex bg-gray-50">
    <!-- Left side with illustration -->
    <div class="hidden lg:flex lg:w-1/2 bg-primary-600 items-center justify-center p-12">
      <div class="max-w-md">
        <NuxtImg src="/illustration.jpg" class="w-full h-auto mb-8 rounded-lg shadow-lg" />
        <h2 class="text-4xl font-bold text-white mb-4">Welcome to TransTRACK</h2>
        <p class="text-primary-100 text-lg">Your comprehensive solution for vehicle tracking and fleet management.</p>
      </div>
    </div>

    <!-- Right side with login form -->
    <div class="w-full lg:w-1/2 flex items-center justify-center p-6 lg:p-8">
      <div class="w-full max-w-md">
        <!-- Logo for mobile view -->
        <div class="text-center lg:hidden mb-6">
          <icon-logo-trans-track class="h-12 w-auto mx-auto" />
        </div>

        <div class="bg-white rounded-xl shadow-lg p-8">
          <div class="text-center mb-8">
            <!-- Logo for desktop view -->
            <div class="hidden lg:block mb-6">
              <icon-logo-trans-track class="h-10 w-auto mx-auto" />
            </div>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">Sign In to Account</h2>
            <p class="text-sm text-gray-600">Enter your credentials to access your account</p>
          </div>

          <form class="space-y-6" @submit.prevent="onSubmitLogin">
            <div class="space-y-4">
              <general-text-input
                v-model="$auth.email"
                id="inputEmail"
                type="text"
                label="Username or Email"
                placeholder="Enter your username or email"
                required
                autocomplete="username"
              >
                <template #prefix>
                  <icon-user size="20" class="stroke-gray-400" />
                </template>
              </general-text-input>

              <general-text-input
                v-model="$auth.password"
                id="inputPass"
                :type="showPass ? 'text' : 'password'"
                label="Password"
                placeholder="Enter your password"
                required
                autocomplete="current-password"
              >
                <template #prefix>
                  <icon-key size="20" class="stroke-gray-400" />
                </template>
                <template #suffix>
                  <button
                    type="button"
                    class="focus:outline-none hover:text-gray-600"
                    @click.stop="showPass = !showPass"
                  >
                    <icon-eye v-if="showPass" size="20" class="stroke-gray-400"/>
                    <icon-eye-off v-else size="20" class="stroke-gray-400"/>
                  </button>
                </template>
              </general-text-input>
            </div>

              <Button
                type="submit"
                label="Sign in"
                :disabled="!$auth.email || !$auth.password"
                :loading="$auth.isLoading"
                variant="primary"
                class="w-full py-3 mt-6"
              />
          </form>
        </div>

        <p class="text-center text-sm text-gray-600 mt-6">
          Need help? <a href="#" class="text-primary-600 hover:text-primary-500">Contact our support team</a>
        </p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-primary-600 {
  background: linear-gradient(135deg, #1a56db, #1e429f);
}
</style>
