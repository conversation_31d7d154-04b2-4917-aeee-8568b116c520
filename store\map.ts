import { defineStore } from 'pinia'
import { useCookie } from '#app'
import type { UnifiedDevice } from '~/types/device'
import type { Position } from '~/types/position'
import type { UnifiedGroup } from '~/types/group'
import type { DeviceLocation } from '~/types/web-socket'

export const useMapStore = defineStore('map', {
    state: () => ({
        devices: [] as UnifiedDevice[],
        positions: [] as Position[],
        selectedDevice: null as UnifiedDevice | null,
        isFollowingDevice: false,
        groups: [] as UnifiedGroup[],
        visibleDevices: new Set<number>(),
        isSidebarOpen: window?.innerWidth >= 768 || false,
        isSidebarMenuOpen: window?.innerWidth >= 768 || false,
        searchQuery: '',
        sortBy: 'name',
        filters: {
            status: [] as string[],
            groups: [] as (string | number)[]
        },
        mapSettings: {
            vesselStatus: {
                showOnline: true,
                showOffline: true,
                showWaiting: true
            },
            vesselElement: {
                showLabel: true,
                showVesselTrail: false,
                showDotTrail: true
            },
            trailSettings: {
                width: 3,
                opacity: 1
            },
            mapType: 'satellite' as 'satellite' | 'vector',
            overlayLayers: {
                showSeaMarks: true,
                showMarineTraffic: true,
                showDepthContours: true,
                showWindy: false
            },
            windySettings: {
                apiKey: 'ayoj6qvPZIDnWX6OsL7l5cpnh5mV9XmF',
                product: 'ecmwf',
                level: 'surface',
                overlay: 'wind',
                timestamp: Math.round(new Date().getTime() / 1000)
            }
        },
        pagination: {
            currentPage: 1,
            itemsPerPage: 50,
            totalItems: 0
        },
        isReFetching: false
    }),

    actions: {
        async toggleDeviceVisibility(deviceId: number) {
            const device = this.devices.find(d => d.id === deviceId)
            if (!device) return

            try {
                // Invert the current visibility state
                const newActiveState = !this.visibleDevices.has(deviceId)

                if (device.telematics) {
                    const response = await fetch('/api/telematics/active', {
                        method: 'POST',
                        body: JSON.stringify({
                            deviceId,
                            active: newActiveState ? 1 : 0
                        })
                    })

                    if (!response.ok) throw new Error('Failed to update device status')

                    if (device.telematics.device_data) {
                        device.telematics.device_data.active = newActiveState ? 1 : 0
                    }
                }

                // Update local visibility state
                if (newActiveState) {
                    this.visibleDevices.add(deviceId)
                } else {
                    this.visibleDevices.delete(deviceId)
                }

            } catch (error) {
                console.error('Error toggling device visibility:', error)
            }
        },

        async toggleGroupVisibility(groupId: undefined | number, value: boolean) {
            try {
                const devices = groupId !== undefined
                    ? (groupId === 0 ? this.ungroupedDevices : this.getDevicesInGroup(groupId))
                    : this.mdvrDevices

                console.log(groupId)

                const hasTelematics = devices.some(device => device.telematics)

                if (hasTelematics) {
                    const response = await fetch('/api/telematics/active', {
                        method: 'POST',
                        body: JSON.stringify({
                            groupId,
                            active: value
                        })
                    })

                    if (!response.ok) {
                        throw new Error('Failed to update group status')
                    }
                }

                const newVisibleDevices = new Set(this.visibleDevices)

                // Update local state regardless of telematics status
                for (const device of devices) {
                    if (value) {
                        newVisibleDevices.add(device.id)

                        if (device.telematics?.device_data) device.telematics.device_data.active = 1
                    } else {
                        newVisibleDevices.delete(device.id)

                        if (device.telematics?.device_data) device.telematics.device_data.active = 0
                    }
                }

                this.visibleDevices = newVisibleDevices
            } catch (error) {
                console.error(`Error toggling group ${groupId} visibility:`, error)
            }
        },

        selectDevice(device: UnifiedDevice) {
            this.selectedDevice = device
            this.isFollowingDevice = true
        },

        toggleFollowDevice(device: UnifiedDevice) {
            if (this.selectedDevice?.id === device.id) {
                this.isFollowingDevice = !this.isFollowingDevice
            } else {
                this.selectedDevice = device
                this.isFollowingDevice = true
            }
        },

        toggleSidebar() {
            this.isSidebarOpen = !this.isSidebarOpen
        },
        toggleMenuSidebar() {
            this.isSidebarMenuOpen = !this.isSidebarMenuOpen
        },

        resetFilters() {
            this.sortBy = 'name'
            this.filters = {
                status: [],
                groups: []
            }
        },

        async initializeDashboard() {
            try {
                const groupsResponse = await fetch('/api/groups')
                this.groups = await groupsResponse.json()

                const devicesResponse = await fetch('/api/devices')
                const newDevices = await devicesResponse.json()

                // Preserve visibility states when updating devices
                const currentVisibleDevices = new Set(this.visibleDevices)

                // Update devices while maintaining existing visibility states
                this.devices = newDevices.map(device => {
                    const isOnlineMdvr = device.mdvr ? device.status === 'online' : false
                    const isActive = device.telematics?.device_data?.active === 1

                    if (isOnlineMdvr || isActive || currentVisibleDevices.has(device.id)) {
                        this.visibleDevices.add(device.id)
                    }

                    return device
                })

                // Fetch positions and update device statuses
                const positionsResponse = await fetch('/api/positions')
                const positionsData = await positionsResponse.json()
                this.positions = Array.isArray(positionsData) && positionsData.length > 0
                    ? positionsData
                    : []

                // Update device statuses without changing visibility
                this.updateDeviceStatuses()
            } catch (error) {
                console.error('Failed to fetch data:', error)
                this.positions = []
                this.groups = []
                this.devices = []
            }
        },

        async fetchPositions() {
            try {
                const response = await fetch('/api/positions')
                const data = await response.json()
                this.positions = Array.isArray(data) ? data : []

                // Don't update visibility states here, only update positions
                if (this.positions.length === 0) {
                    const devicesResponse = await fetch('/api/devices')
                    const newDevices = await devicesResponse.json()

                    // Update devices while preserving visibility states
                    const currentVisibleDevices = new Set(this.visibleDevices)
                    this.devices = newDevices.map(device => {
                        if (currentVisibleDevices.has(device.id)) {
                            this.visibleDevices.add(device.id)
                        }
                        return device
                    })
                }

                // Update device statuses without changing visibility
                this.updateDeviceStatuses()
            } catch (error) {
                console.error('Failed to fetch positions:', error)
                this.positions = []
            }
        },

        // New helper method to update device statuses
        updateDeviceStatuses() {
            this.devices.forEach(device => {
                const position = this.positions.find(pos => pos.deviceId === device.id)
                if (position) {
                    const positionTime = new Date(position.deviceTime).getTime()
                    const currentTime = Date.now()
                    const timeDiff = currentTime - positionTime

                    if (timeDiff > 300000) { // 5 minutes
                        device.status = 'offline'
                    } else if (position.speed > 0) {
                        device.status = 'online'
                    } else {
                        device.status = 'waiting'
                    }
                }
            })
        },

        updateMapSettings(settings: Partial<typeof this.mapSettings>) {
            this.mapSettings = {
                ...this.mapSettings,
                ...settings
            }
        },

        toggleVesselStatus(key: keyof typeof this.mapSettings.vesselStatus) {
            this.mapSettings.vesselStatus[key] = !this.mapSettings.vesselStatus[key]
        },

        toggleVesselElement(key: keyof typeof this.mapSettings.vesselElement) {
            const newValue = !this.mapSettings.vesselElement[key]
            this.mapSettings.vesselElement = {
                ...this.mapSettings.vesselElement,
                [key]: newValue,
                // If disabling vessel trail, also disable dot trail
                ...(key === 'showVesselTrail' && !newValue ? { showDotTrail: false } : {})
            }
        },

        updateTrailSettings(settings: Partial<typeof this.mapSettings.trailSettings>) {
            this.mapSettings.trailSettings = {
                ...this.mapSettings.trailSettings,
                ...settings
            }
        },

        setMapType(type: 'satellite' | 'vector') {
            this.mapSettings.mapType = type
        },

        toggleOverlayLayer(key: keyof typeof this.mapSettings.overlayLayers) {
            this.mapSettings.overlayLayers[key] = !this.mapSettings.overlayLayers[key]
        },

        updateWindySettings(settings: Partial<typeof this.mapSettings.windySettings>) {
            this.mapSettings.windySettings = {
                ...this.mapSettings.windySettings,
                ...settings
            }
        },

        toggleWindy() {
            this.mapSettings.overlayLayers.showWindy = !this.mapSettings.overlayLayers.showWindy
        },

        resetMapSettings() {
            this.mapSettings = {
                vesselStatus: {
                    showOnline: true,
                    showOffline: true,
                    showWaiting: true
                },
                vesselElement: {
                    showLabel: true,
                    showVesselTrail: false,
                    showDotTrail: false
                },
                trailSettings: {
                    width: 3,
                    opacity: 1
                },
                mapType: 'satellite',
                overlayLayers: {
                    showSeaMarks: true,
                    showMarineTraffic: true,
                    showDepthContours: true,
                    showWindy: false
                },
                windySettings: {
                    apiKey: 'PsLAtXpsPTZexBwUkO7Mx5I',
                    product: 'ecmwf',
                    level: 'surface',
                    overlay: 'wind',
                    timestamp: Math.round(new Date().getTime() / 1000)
                }
            }
        },

        updateDevicePosition(deviceLocation: DeviceLocation) {
            const device = this.devices.find(d => d.telematics?.device_data.imei === deviceLocation.imei)

            if (!device) return

            Object.assign(device, {
                altitude: deviceLocation.altitude,
                course: deviceLocation.course,
                lat: Number(deviceLocation.lng),
                lng: Number(deviceLocation.lat)
            })
        }
    },

    getters: {
        // Correctly typed getter functions
        getDevicesInGroup(): (groupId: number) => UnifiedDevice[] {
            return (groupId: number): UnifiedDevice[] => {
                if (typeof groupId === 'undefined') {
                    return []
                }
                return this.sortedAndFilteredDevices.filter(device => device.groupId === groupId)
            }
        },

        sortedAndFilteredDevices(): UnifiedDevice[] {
            return this.devices
                .filter(device => {
                    const searchTerm = this.searchQuery?.toLowerCase() || ''
                    const deviceName = device.name?.toLowerCase() || ''
                    const deviceCategory = device.traccar?.category?.toLowerCase() || ''
                    return deviceName.includes(searchTerm) || deviceCategory.includes(searchTerm)
                })
                .filter(device => {
                    const matchesStatus = this.filters.status.length === 0 ||
                        this.filters.status.includes(device.status.toLowerCase())
                    const matchesGroup = this.filters.groups.length === 0 ||
                        (this.filters.groups.includes('ungrouped') && !device.groupId) ||
                        (device.groupId && this.filters.groups.includes(device.groupId))
                    return matchesStatus && matchesGroup
                })
                .sort((a, b) => {
                    switch (this.sortBy) {
                        case 'name':
                            return a.name.localeCompare(b.name)
                        case 'name-desc':
                            return b.name.localeCompare(a.name)
                        case 'status':
                            return a.status.localeCompare(b.status)
                        case 'last-updated':
                            const aLastUpdate = a.traccar?.lastUpdate || a.telematics?.time || ''
                            const bLastUpdate = b.traccar?.lastUpdate || b.telematics?.time || ''
                            return new Date(bLastUpdate).getTime() - new Date(aLastUpdate).getTime()
                        default:
                            return 0
                    }
                })
        },

        mdvrDevices(): UnifiedDevice[] {
            return this.sortedAndFilteredDevices.filter(device => device.mdvr)
        },

        ungroupedDevices(): UnifiedDevice[] {
            return this.sortedAndFilteredDevices.filter(device => !device.groupId && !device.mdvr)
        },

        getDevicePosition(): (device: UnifiedDevice) => Position | undefined {
            return (device: UnifiedDevice): Position | undefined => {
                if (!device?.id) return undefined;
                const position = this.positions.find(pos => pos?.deviceId === device.id);
                if (!position) {
                    // Create a default position if none exists
                    const now = new Date().toISOString();

                    return {
                        id: device.id,
                        deviceId: device.id,
                        protocol: device.source,
                        deviceTime: device.telematics?.time || device.traccar?.lastUpdate || now,
                        fixTime: device.telematics?.time || device.traccar?.lastUpdate || now,
                        serverTime: now,
                        outdated: false,
                        valid: true,
                        latitude: device.lat || device.telematics?.lat || 47.21322,
                        longitude: device.lng || device.telematics?.lng || -1.559482,
                        altitude: device.altitude || device.telematics?.altitude || 0,
                        speed: device.speed || device.telematics?.speed || 0,
                        course: device.course || device.telematics?.course || 0,
                        address: null,
                        accuracy: 0,
                        network: null,
                        attributes: device.traccar?.attributes || {},
                        tail: device.telematics?.tail ?
                            Array.isArray(device.telematics.tail)
                                ? device.telematics.tail.map(t => `${t.lat}/${t.lng}`).join(';')
                                : ''
                            : ''
                    };
                }
                return position;
            }
        },

        isGroupVisible(): (groupId: number) => boolean {
            return (groupId: number) => {
                const groupDevices = this.getDevicesInGroup(groupId)
                return groupDevices.length > 0 && groupDevices.every(device =>
                    device.telematics?.device_data?.active === 1 &&
                    this.visibleDevices.has(device.id)
                )
            }
        },

        isMdvrVisible(): boolean {
            return this.mdvrDevices.length > 0 && this.mdvrDevices.every(device => (
                this.visibleDevices.has(device.id)
            ))
        },

        isUngroupedVisible(): boolean {
            return this.ungroupedDevices.length > 0 && this.ungroupedDevices.every(device =>
                device.telematics?.device_data?.active === 1 &&
                this.visibleDevices.has(device.id)
            )
        },

        hasActiveFilters(): boolean {
            return this.filters.status.length < 3 ||
                this.sortBy !== 'name' ||
                this.filters.groups.length > 0
        },

        filteredGroups(): UnifiedGroup[] {
            return this.groups
                .filter(group => {
                    const groupDevices = this.getDevicesInGroup(group.id)
                    return groupDevices.length > 0 && (
                        this.filters.groups.length === 0 ||
                        this.filters.groups.includes(group.id)
                    )
                })
        },

        paginatedDevices(): UnifiedDevice[] {
            const start = (this.pagination.currentPage - 1) * this.pagination.itemsPerPage
            const end = start + this.pagination.itemsPerPage
            return this.filteredDevices.slice(start, end)
        },

        filteredDevices(): UnifiedDevice[] {
            let filtered = [...this.devices]

            // Apply status filters
            if (this.filters.status.length > 0) {
                filtered = filtered.filter(device =>
                    this.filters.status.includes(device.status)
                )
            }

            // Apply group filters
            if (this.filters.groups.length > 0) {
                filtered = filtered.filter(device =>
                    this.filters.groups.includes(device.groupId || 0)
                )
            }

            // Apply search
            if (this.searchQuery) {
                const query = this.searchQuery.toLowerCase()
                filtered = filtered.filter(device =>
                    device.name.toLowerCase().includes(query)
                )
            }

            // Update total items
            this.pagination.totalItems = filtered.length

            return filtered
        },

        totalPages(): number {
            return Math.ceil(this.pagination.totalItems / this.pagination.itemsPerPage)
        }
    }
})


