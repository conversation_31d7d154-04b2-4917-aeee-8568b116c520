<script lang="ts" setup>
import type {ElementEvent} from "~/types/element";
import {computed} from "vue";
import {useMapStore} from "~/store/map";

const store = useMapStore()

const isMobile = computed(() => {
  return window.innerWidth <= 640
})

const {status} = useAuth()

const routePath = computed((): string => {
  return useRoute().path
})

let modalWarningUnauthorized: ElementEvent | null = null

watch(status, (newStatus) => {
  if (newStatus === 'unauthenticated') {
    modalWarningUnauthorized?.show();
  }
})

watch(() => useIsUnauthorized().value, (isUnauthorized) => {
  if (isUnauthorized) modalWarningUnauthorized?.show()
})

function onClickBackToLogin() {
  useCookie('auth:token').value = undefined
  clearCookies()
  window.location.href = '/login'
}

</script>

<template>
  <general-modal-warning id="modal-warning-unauthorized" title="Unauthorized"
    subtitle="User unauthorized, please log in again." button-label="Back to Login"
    @on-mounted="modalWarningUnauthorized = $event" @on-click-button="onClickBackToLogin">
    <template #icon>
      <illustration-cloud-hourglass class="max-w-[160px] h-fit"/>
    </template>
  </general-modal-warning>
  
  <device-sidebar class="h-screen w-full">
    <template #default>
      <div v-if="!routePath.startsWith('/dashboard')" class="sticky top-0 bg-white z-50">
        <Breadcrumbs/>
      </div>
      <div class="h-full">
        <slot />
      </div>
    </template>
  </device-sidebar>
</template>

